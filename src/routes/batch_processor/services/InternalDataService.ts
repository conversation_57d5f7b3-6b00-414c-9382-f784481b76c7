/**
 * 抖音内部底稿数据服务
 *
 * 功能：
 * 1. 封装底稿数据接口调用
 * 2. 实现缓存机制提升性能
 * 3. 提供完善的错误处理
 * 4. 支持超时控制
 */

import createLogger from '../utils/logger';

const logger = createLogger('InternalDataService');

/**
 * 底稿数据响应接口
 */
export interface InternalDataResponse {
  success: boolean;
  data: any;
  summary?: string;
  error?: string;
  source: 'cache' | 'api';
  timestamp: number;
}

/**
 * 底稿数据错误类型
 */
export enum InternalDataError {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  API_ERROR = 'API_ERROR',
  INVALID_QUERY = 'INVALID_QUERY',
}

/**
 * 缓存项接口
 */
interface CacheItem {
  data: InternalDataResponse;
  expireTime: number;
}

/**
 * 抖音内部底稿数据服务类
 */
export class InternalDataService {
  private static readonly BASE_URL =
    'http://9gzj7t9k.fn.bytedance.net/api/search/stream';
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
  private static readonly REQUEST_TIMEOUT = 30000; // 30秒超时
  private static cache = new Map<string, CacheItem>();

  /**
   * 获取底稿数据
   * @param query 查询关键词
   * @param summaryOnly 是否只返回摘要
   * @returns 底稿数据响应
   */
  static async fetchInternalData(
    query: string,
    summaryOnly: boolean = true,
  ): Promise<InternalDataResponse> {
    // 参数验证
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      logger.error('[fetchInternalData] 无效的查询参数:', query);
      return this.createErrorResponse(
        InternalDataError.INVALID_QUERY,
        '查询参数不能为空',
      );
    }

    const trimmedQuery = query.trim();
    const cacheKey = `${trimmedQuery}_${summaryOnly}`;

    // 检查缓存
    const cachedResult = this.getCachedData(cacheKey);
    if (cachedResult) {
      logger.info('[fetchInternalData] 使用缓存数据:', {
        query: trimmedQuery,
        cacheKey,
      });
      return cachedResult;
    }

    // 记录请求开始
    const startTime = Date.now();
    logger.info('[fetchInternalData] 开始请求底稿数据:', {
      query: trimmedQuery,
      summaryOnly,
      timestamp: new Date().toISOString(),
    });

    try {
      // 构建请求URL
      const url = this.buildRequestUrl(trimmedQuery, summaryOnly);

      // 创建超时控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        logger.warn('[fetchInternalData] 请求超时:', {
          query: trimmedQuery,
          timeout: this.REQUEST_TIMEOUT,
        });
      }, this.REQUEST_TIMEOUT);

      // 发送请求
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        signal: controller.signal,
        // 添加请求配置
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'same-origin',
      });

      // 清除超时定时器
      clearTimeout(timeoutId);

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text().catch(() => '无法获取错误详情');
        logger.error('[fetchInternalData] API请求失败:', {
          query: trimmedQuery,
          status: response.status,
          statusText: response.statusText,
          errorText: errorText.substring(0, 200),
        });

        return this.createErrorResponse(
          InternalDataError.API_ERROR,
          `接口请求失败: ${response.status} ${response.statusText}`,
        );
      }

      // 解析响应数据
      const responseData = await response.json();
      const duration = Date.now() - startTime;

      logger.info('[fetchInternalData] 底稿数据获取成功:', {
        query: trimmedQuery,
        duration,
        dataSize: JSON.stringify(responseData).length,
      });

      // 构建成功响应
      const result: InternalDataResponse = {
        success: true,
        data: responseData,
        summary: responseData.summary || this.extractSummary(responseData),
        source: 'api',
        timestamp: Date.now(),
      };

      // 缓存结果
      this.setCachedData(cacheKey, result);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      // 处理不同类型的错误
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          logger.error('[fetchInternalData] 请求超时:', {
            query: trimmedQuery,
            duration,
          });
          return this.createErrorResponse(
            InternalDataError.TIMEOUT_ERROR,
            '请求超时，请稍后重试',
          );
        }

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          logger.error('[fetchInternalData] 网络错误:', {
            query: trimmedQuery,
            error: error.message,
          });
          return this.createErrorResponse(
            InternalDataError.NETWORK_ERROR,
            '网络连接失败，请检查网络设置',
          );
        }

        if (error.name === 'SyntaxError') {
          logger.error('[fetchInternalData] 数据解析错误:', {
            query: trimmedQuery,
            error: error.message,
          });
          return this.createErrorResponse(
            InternalDataError.PARSE_ERROR,
            '数据格式错误，无法解析响应',
          );
        }
      }

      logger.error('[fetchInternalData] 未知错误:', {
        query: trimmedQuery,
        duration,
        error: error instanceof Error ? error.message : String(error),
      });

      return this.createErrorResponse(
        InternalDataError.NETWORK_ERROR,
        error instanceof Error ? error.message : '未知错误',
      );
    }
  }

  /**
   * 构建请求URL
   * @param query 查询关键词
   * @param summaryOnly 是否只返回摘要
   * @returns 完整的请求URL
   */
  private static buildRequestUrl(query: string, summaryOnly: boolean): string {
    const url = new URL(this.BASE_URL);
    url.searchParams.set('query', encodeURIComponent(query));
    url.searchParams.set('summary_only', summaryOnly ? '1' : '0');

    logger.debug('[buildRequestUrl] 构建请求URL:', {
      query,
      summaryOnly,
      url: url.toString(),
    });

    return url.toString();
  }

  /**
   * 获取缓存数据
   * @param cacheKey 缓存键
   * @returns 缓存的数据或null
   */
  private static getCachedData(cacheKey: string): InternalDataResponse | null {
    const cacheItem = this.cache.get(cacheKey);

    if (!cacheItem) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > cacheItem.expireTime) {
      this.cache.delete(cacheKey);
      logger.debug('[getCachedData] 缓存已过期，删除缓存项:', { cacheKey });
      return null;
    }

    // 更新source标记为cache
    const cachedData = { ...cacheItem.data, source: 'cache' as const };
    logger.debug('[getCachedData] 命中缓存:', {
      cacheKey,
      timestamp: cacheItem.data.timestamp,
    });

    return cachedData;
  }

  /**
   * 设置缓存数据
   * @param cacheKey 缓存键
   * @param data 要缓存的数据
   */
  private static setCachedData(
    cacheKey: string,
    data: InternalDataResponse,
  ): void {
    const expireTime = Date.now() + this.CACHE_DURATION;
    this.cache.set(cacheKey, { data, expireTime });

    logger.debug('[setCachedData] 数据已缓存:', {
      cacheKey,
      expireTime: new Date(expireTime).toISOString(),
      cacheSize: this.cache.size,
    });

    // 清理过期缓存（防止内存泄漏）
    this.cleanExpiredCache();
  }

  /**
   * 清理过期缓存
   */
  private static cleanExpiredCache(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('[cleanExpiredCache] 清理过期缓存:', {
        cleanedCount,
        remainingSize: this.cache.size,
      });
    }
  }

  /**
   * 创建错误响应
   * @param errorType 错误类型
   * @param message 错误消息
   * @returns 错误响应对象
   */
  private static createErrorResponse(
    errorType: InternalDataError,
    message: string,
  ): InternalDataResponse {
    return {
      success: false,
      data: null,
      error: message,
      source: 'api',
      timestamp: Date.now(),
    };
  }

  /**
   * 从响应数据中提取摘要
   * @param data 响应数据
   * @returns 提取的摘要
   */
  private static extractSummary(data: any): string {
    if (!data) return '';

    // 尝试从不同字段提取摘要
    if (typeof data.summary === 'string') return data.summary;
    if (typeof data.description === 'string') return data.description;
    if (typeof data.content === 'string') return data.content.substring(0, 200);
    if (typeof data.title === 'string') return data.title;

    // 如果是数组，尝试提取第一个元素的信息
    if (Array.isArray(data) && data.length > 0) {
      const firstItem = data[0];
      if (typeof firstItem === 'string') return firstItem.substring(0, 200);
      if (typeof firstItem === 'object' && firstItem !== null) {
        return this.extractSummary(firstItem);
      }
    }

    // 如果是对象，尝试提取有用信息
    if (typeof data === 'object' && data !== null) {
      const keys = Object.keys(data);
      for (const key of keys) {
        if (typeof data[key] === 'string' && data[key].length > 10) {
          return data[key].substring(0, 200);
        }
      }
    }

    return '暂无摘要信息';
  }

  /**
   * 清空所有缓存
   */
  static clearCache(): void {
    const cacheSize = this.cache.size;
    this.cache.clear();
    logger.info('[clearCache] 已清空所有缓存:', { previousSize: cacheSize });
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  static getCacheStats(): {
    size: number;
    keys: string[];
    totalMemoryUsage: number;
  } {
    const keys = Array.from(this.cache.keys());
    let totalMemoryUsage = 0;

    // 估算内存使用量
    for (const [key, item] of this.cache.entries()) {
      totalMemoryUsage += key.length * 2; // 字符串按2字节计算
      totalMemoryUsage += JSON.stringify(item.data).length * 2;
    }

    return {
      size: this.cache.size,
      keys,
      totalMemoryUsage,
    };
  }

  /**
   * 预热缓存（批量预加载常用查询）
   * @param queries 要预加载的查询列表
   */
  static async warmupCache(queries: string[]): Promise<void> {
    logger.info('[warmupCache] 开始预热缓存:', { queryCount: queries.length });

    const promises = queries.map(query =>
      this.fetchInternalData(query, true).catch(error => {
        logger.warn('[warmupCache] 预热失败:', { query, error: error.message });
        return null;
      }),
    );

    const results = await Promise.allSettled(promises);
    const successCount = results.filter(
      result => result.status === 'fulfilled',
    ).length;

    logger.info('[warmupCache] 缓存预热完成:', {
      total: queries.length,
      success: successCount,
      cacheSize: this.cache.size,
    });
  }
}
