/**
 * 数据处理服务
 *
 * 🎯 核心职责：
 * 1. 处理底稿数据与AI prompt的智能结合
 * 2. 确保AI严格使用底稿数据进行内容生成
 * 3. 提供数据验证、格式化和展示功能
 * 4. 支持模式切换和优雅回退机制
 * 5. 构建符合要求的严格数据使用prompt
 *
 * 🔄 工作流程：
 * 1. 接收用户查询和模式选择
 * 2. 根据模式决定是否获取底稿数据
 * 3. 验证和处理底稿数据的完整性
 * 4. 构建严格使用底稿数据的AI prompt
 * 5. 返回处理结果供AI生成使用
 *
 * 🚨 严格数据使用原则：
 * - 必须严格使用底稿数据中的具体内容
 * - 保持HTML标记和特殊引用标记的完整性
 * - 不得大幅修改核心信息和统计数据
 * - 只允许轻微的UI结构和排版调整
 * - 确保参考资料的来源信息准确性
 *
 * 💡 设计模式：
 * - 策略模式：根据不同模式采用不同的处理策略
 * - 模板方法：标准化的数据处理流程
 * - 工厂模式：根据数据类型创建不同的处理器
 *
 * <AUTHOR>
 * @since 2025-07-28
 * @version 1.0.0
 */

import {
  InternalDataService,
  InternalDataResponse,
  InternalDataError,
} from './InternalDataService';
import createLogger from '../../code_generate/utils/logger';

const logger = createLogger('DataProcessingService');

/**
 * 查询处理结果接口
 *
 * 📊 结果数据结构说明：
 * - processedQuery: 处理后的查询内容，包含严格使用底稿数据的prompt
 * - context: 底稿数据上下文，包含原始的answer、pv、reference等信息
 * - source: 数据源标识，'internal'表示使用底稿数据，'direct'表示直接AI生成
 * - originalQuery: 用户的原始查询内容，用于对比和日志记录
 * - timestamp: 处理完成的时间戳，用于性能监控和调试
 * - internalDataSummary: 底稿数据的摘要信息，便于用户了解数据来源
 *
 * 💡 使用示例：
 * ```typescript
 * const result = await DataProcessingService.processQuery('九九乘法表', true);
 * if (result.source === 'internal') {
 *   console.log('使用了底稿数据:', result.internalDataSummary);
 *   console.log('处理后的prompt:', result.processedQuery);
 * }
 * ```
 */
export interface QueryProcessingResult {
  /** 处理后的查询内容，包含严格使用底稿数据的完整prompt */
  processedQuery: string;
  /** 底稿数据上下文，包含answer、pv、reference等原始信息 */
  context?: any;
  /** 数据源标识：'internal'使用底稿数据，'direct'直接AI生成 */
  source: 'internal' | 'direct';
  /** 用户输入的原始查询内容 */
  originalQuery: string;
  /** 处理完成的时间戳，用于性能监控 */
  timestamp: number;
  /** 底稿数据的摘要信息，便于用户了解数据来源和内容概要 */
  internalDataSummary?: string;
}

/**
 * 数据处理配置接口
 *
 * ⚙️ 配置参数说明：
 * - strictMode: 严格模式开关，启用时必须严格遵循底稿数据
 * - allowDataModification: 是否允许对底稿数据进行轻微调整
 * - maxDataSize: 底稿数据的最大大小限制，防止处理过大的数据
 * - timeoutMs: 数据处理的超时时间，避免长时间阻塞
 *
 * 🔧 配置策略：
 * - 严格模式确保数据准确性和权威性
 * - 允许轻微修改以提升用户体验和UI适配
 * - 大小限制防止性能问题和内存溢出
 * - 超时控制确保系统响应性
 */
export interface DataProcessingConfig {
  /** 严格模式：启用时必须严格使用底稿数据，不允许大幅修改 */
  strictMode: boolean;
  /** 是否允许轻微修改数据：如UI结构调整、字数优化等 */
  allowDataModification: boolean;
  /** 最大数据大小限制（字节），超过此大小的数据将被截断 */
  maxDataSize: number;
  /** 处理超时时间（毫秒），超时后将终止处理 */
  timeoutMs: number;
}

/**
 * 默认配置
 *
 * 📋 配置说明：
 * - strictMode: true - 启用严格模式，确保底稿数据的准确性
 * - allowDataModification: true - 允许轻微调整，提升用户体验
 * - maxDataSize: 100KB - 合理的数据大小限制，平衡性能和功能
 * - timeoutMs: 10秒 - 适中的超时时间，确保响应性
 *
 * 🎯 设计原则：
 * - 数据准确性优先：严格模式确保信息权威性
 * - 用户体验兼顾：允许必要的UI优化调整
 * - 性能可控：合理的大小和时间限制
 * - 配置灵活：可通过setConfig方法动态调整
 */
const DEFAULT_CONFIG: DataProcessingConfig = {
  /** 启用严格模式，确保严格使用底稿数据 */
  strictMode: true,
  /** 允许轻微调整结构、字数和排版，提升移动端体验 */
  allowDataModification: true,
  /** 最大数据大小：100KB，防止处理过大数据影响性能 */
  maxDataSize: 100 * 1024,
  /** 处理超时时间：10秒，确保系统响应性 */
  timeoutMs: 10000,
};

/**
 * 数据处理服务类
 *
 * 🏗️ 架构特点：
 * - 静态类设计：所有方法都是静态方法，便于全局调用
 * - 配置驱动：通过配置参数控制处理行为
 * - 策略模式：根据模式选择不同的处理策略
 * - 错误容错：完善的错误处理和回退机制
 *
 * 🔄 处理流程：
 * 1. 模式判断：根据useInternalData参数决定处理方式
 * 2. 数据获取：调用InternalDataService获取底稿数据
 * 3. 数据验证：验证底稿数据的完整性和有效性
 * 4. Prompt构建：构建严格使用底稿数据的AI prompt
 * 5. 结果返回：返回包含处理结果的标准化对象
 *
 * 💡 核心价值：
 * - 确保AI严格使用底稿数据，保证信息准确性
 * - 提供统一的数据处理接口，简化上层调用
 * - 支持灵活的配置和扩展，适应不同需求
 */
export class DataProcessingService {
  /** 当前使用的数据处理配置 */
  private static config: DataProcessingConfig = DEFAULT_CONFIG;

  /**
   * 设置处理配置
   * @param config 新的配置
   */
  static setConfig(config: Partial<DataProcessingConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('[setConfig] 配置已更新:', this.config);
  }

  /**
   * 处理查询请求 - 核心方法
   *
   * 🎯 方法职责：
   * - 根据模式选择决定数据处理策略
   * - 获取和验证抖音内部底稿数据
   * - 构建严格使用底稿数据的AI prompt
   * - 提供完整的错误处理和回退机制
   * - 实时报告处理进度给调用方
   *
   * 🔄 处理流程：
   * 1. 参数验证和初始化（进度：0%）
   * 2. 模式判断：直接模式直接返回，底稿模式继续处理
   * 3. 获取底稿数据：调用InternalDataService（进度：10-50%）
   * 4. 数据验证：检查底稿数据的完整性和有效性（进度：50-70%）
   * 5. Prompt构建：创建严格使用底稿数据的prompt（进度：70-90%）
   * 6. 结果封装：返回标准化的处理结果（进度：100%）
   *
   * 🚨 错误处理：
   * - 网络错误：自动重试或回退到直接模式
   * - 数据无效：记录警告并回退到直接模式
   * - 超时错误：终止处理并回退到直接模式
   * - 所有错误都不会影响用户的正常使用体验
   *
   * @param query 用户输入的原始查询内容，不能为空
   * @param useInternalData 是否使用抖音内部底稿数据模式
   * @param onProgress 可选的进度回调函数，参数为0-1之间的进度值
   * @returns Promise<QueryProcessingResult> 包含处理结果的标准化对象
   *
   * @example
   * ```typescript
   * // 使用底稿数据模式
   * const result = await DataProcessingService.processQuery(
   *   '九九乘法表',
   *   true,
   *   (progress) => console.log(`进度: ${Math.round(progress * 100)}%`)
   * );
   *
   * if (result.source === 'internal') {
   *   console.log('使用了底稿数据:', result.internalDataSummary);
   * }
   * ```
   */
  static async processQuery(
    query: string,
    useInternalData: boolean,
    onProgress?: (progress: number) => void,
  ): Promise<QueryProcessingResult> {
    const startTime = Date.now();

    logger.info('[processQuery] 开始处理查询:', {
      query: query.substring(0, 50),
      useInternalData,
      timestamp: new Date().toISOString(),
    });

    // 如果不使用内部数据，直接返回原始查询
    if (!useInternalData) {
      onProgress?.(1.0);
      return {
        processedQuery: query,
        source: 'direct',
        originalQuery: query,
        timestamp: Date.now(),
      };
    }

    try {
      onProgress?.(0.1); // 10% - 开始获取底稿数据

      // 获取底稿数据
      const internalDataResponse = await InternalDataService.fetchInternalData(
        query,
        true,
      );

      onProgress?.(0.5); // 50% - 底稿数据获取完成

      // 检查数据获取是否成功
      if (!internalDataResponse.success) {
        logger.warn('[processQuery] 底稿数据获取失败，回退到直接模式:', {
          query: query.substring(0, 50),
          error: internalDataResponse.error,
        });

        onProgress?.(1.0);
        return {
          processedQuery: query,
          source: 'direct',
          originalQuery: query,
          timestamp: Date.now(),
        };
      }

      onProgress?.(0.7); // 70% - 开始构建增强查询

      // 验证数据大小
      const dataSize = JSON.stringify(internalDataResponse.data).length;
      if (dataSize > this.config.maxDataSize) {
        logger.warn('[processQuery] 底稿数据过大，回退到直接模式:', {
          query: query.substring(0, 50),
          dataSize,
          maxSize: this.config.maxDataSize,
        });

        onProgress?.(1.0);
        return {
          processedQuery: query,
          source: 'direct',
          originalQuery: query,
          timestamp: Date.now(),
        };
      }

      // 构建增强的查询上下文
      const enhancedQuery = this.buildEnhancedQuery(
        query,
        internalDataResponse.data,
      );

      onProgress?.(0.9); // 90% - 查询增强完成

      const duration = Date.now() - startTime;
      logger.info('[processQuery] 查询处理完成:', {
        query: query.substring(0, 50),
        source: 'internal',
        duration,
        dataSource: internalDataResponse.source,
      });

      onProgress?.(1.0); // 100% - 完成

      return {
        processedQuery: enhancedQuery,
        context: internalDataResponse.data,
        source: 'internal',
        originalQuery: query,
        timestamp: Date.now(),
        internalDataSummary: internalDataResponse.summary,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('[processQuery] 数据处理失败，回退到直接模式:', {
        query: query.substring(0, 50),
        duration,
        error: error instanceof Error ? error.message : String(error),
      });

      onProgress?.(1.0);
      return {
        processedQuery: query,
        source: 'direct',
        originalQuery: query,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 构建增强的查询prompt - 严格数据使用的核心实现
   *
   * 🎯 核心目标：
   * - 构建明确指示AI严格使用底稿数据的prompt
   * - 确保底稿数据的完整性和准确性得到保持
   * - 允许合理的UI优化，但禁止内容篡改
   * - 提供清晰的数据结构和使用指南
   *
   * 📋 Prompt结构：
   * 1. 重要指令：明确要求严格使用底稿数据
   * 2. 用户查询：展示原始查询内容
   * 3. 底稿数据：完整展示核心数据字段
   * 4. 严格要求：详细的数据使用规则
   * 5. UI生成要求：移动端适配和用户体验指导
   *
   * 🚨 严格要求说明：
   * - 数据准确性：不得修改核心内容，保持HTML标记
   * - 允许调整：UI结构、字体、颜色等视觉优化
   * - 禁止操作：删除来源信息、修改统计数据、大幅改动内容
   *
   * 💡 设计原则：
   * - 信息完整：确保所有重要信息都被包含
   * - 指令明确：使用清晰的标记和分类
   * - 约束严格：明确禁止和允许的操作
   * - 体验优化：兼顾数据准确性和用户体验
   *
   * @param originalQuery 用户输入的原始查询内容
   * @param internalData 从底稿数据接口获取的完整数据对象
   * @returns 包含严格使用指令的完整AI prompt字符串
   */
  private static buildEnhancedQuery(
    originalQuery: string,
    internalData: any,
  ): string {
    // 提取底稿数据的关键字段
    const answer = internalData.answer || '';
    const reference = internalData.reference || '';
    const pv = internalData.pv || 0;
    const reasoning = internalData.reasoning || '';

    // 构建严格使用底稿数据的prompt
    const strictDataPrompt = `
🎯 **重要指令：严格使用抖音内部底稿数据进行UI生成**

用户查询：${originalQuery}

📋 **底稿数据（必须严格遵循）：**

**核心答案内容：**
${answer}

**参考资料：**
${reference}

**数据热度：** ${pv} 次浏览
${reasoning ? `**推理过程：** ${reasoning}` : ''}

🚨 **严格要求：**
1. **数据准确性**：
   - 必须严格使用上述底稿数据中的具体内容和数据
   - 核心答案内容不得修改，保持原有的标记格式（如 <mark> 标签）
   - 参考资料中的链接、日期、来源信息必须保持准确
   - 不得编造或添加底稿数据中不存在的信息

2. **允许的轻微调整**：
   - 可以优化UI结构和布局以提升移动端用户体验
   - 可以调整字体大小、颜色搭配和视觉层次
   - 可以重新组织信息的展示顺序，但不得删除重要内容
   - 可以添加适当的图标和分隔线来改善视觉效果

3. **禁止的操作**：
   - 不得大幅度修改底稿数据中的核心信息和数据
   - 不得删除参考资料中的来源信息和链接
   - 不得修改数据热度（pv值）等统计信息
   - 不得改变答案的核心含义和关键事实

🎨 **UI生成要求：**
- 优先展示核心答案内容，确保信息清晰易读
- 合理展示参考资料，保持来源的可追溯性
- 在移动端界面中突出重点信息，保持良好的信息层次
- 使用适当的视觉元素增强用户体验，但不影响信息准确性
- 确保所有底稿数据中的关键信息都得到完整展示

请严格按照上述抖音内部底稿数据生成相应的移动端UI界面，确保数据的准确性、完整性和权威性。`;

    return strictDataPrompt;
  }

  /**
   * 验证底稿数据的有效性
   * @param data 底稿数据
   * @returns 是否有效
   */
  private static validateInternalData(data: any): boolean {
    if (!data) {
      logger.warn('[validateInternalData] 底稿数据为空');
      return false;
    }

    // 检查数据类型
    if (typeof data !== 'object') {
      logger.warn('[validateInternalData] 底稿数据类型无效:', typeof data);
      return false;
    }

    // 检查必要字段
    if (!data.answer || typeof data.answer !== 'string') {
      logger.warn('[validateInternalData] 缺少有效的answer字段');
      return false;
    }

    // 检查数据大小
    const dataSize = JSON.stringify(data).length;
    if (dataSize > this.config.maxDataSize) {
      logger.warn('[validateInternalData] 底稿数据过大:', {
        dataSize,
        maxSize: this.config.maxDataSize,
      });
      return false;
    }

    // 检查answer内容是否有意义
    if (data.answer.trim().length < 10) {
      logger.warn('[validateInternalData] answer内容过短，可能无效');
      return false;
    }

    return true;
  }

  /**
   * 检查数据是否包含有用内容
   * @param data 数据对象
   * @returns 是否包含有用内容
   */
  private static hasUsefulContent(data: any): boolean {
    if (typeof data === 'string' && data.trim().length > 0) {
      return true;
    }

    if (Array.isArray(data) && data.length > 0) {
      return data.some(item => this.hasUsefulContent(item));
    }

    if (typeof data === 'object' && data !== null) {
      const values = Object.values(data);
      return values.some(value => this.hasUsefulContent(value));
    }

    return false;
  }

  /**
   * 格式化底稿数据用于显示
   * @param data 底稿数据
   * @returns 格式化后的数据
   */
  static formatInternalDataForDisplay(data: any): string {
    if (!data) return '暂无数据';

    try {
      // 如果是抖音底稿数据结构，进行特殊格式化
      if (typeof data === 'object' && data.answer) {
        const parts: string[] = [];

        // 添加核心答案
        if (data.answer) {
          parts.push(`📝 核心答案：\n${data.answer}`);
        }

        return parts.join('\n\n');
      }

      // 如果是字符串，直接返回
      if (typeof data === 'string') {
        return data.length > 200 ? data.substring(0, 200) + '...' : data;
      }

      // 如果是其他对象或数组，格式化为JSON
      const jsonString = JSON.stringify(data, null, 2);
      return jsonString.length > 500
        ? jsonString.substring(0, 500) + '...'
        : jsonString;
    } catch (error) {
      logger.error('[formatInternalDataForDisplay] 格式化失败:', error);
      return '数据格式化失败';
    }
  }

  /**
   * 获取处理统计信息
   * @returns 统计信息
   */
  static getProcessingStats(): {
    config: DataProcessingConfig;
    cacheStats: any;
  } {
    return {
      config: this.config,
      cacheStats: InternalDataService.getCacheStats(),
    };
  }
}
