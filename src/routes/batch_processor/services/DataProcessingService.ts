/**
 * 数据处理服务
 * 
 * 功能：
 * 1. 处理底稿数据与AI prompt的结合
 * 2. 确保严格使用底稿数据进行UI生成
 * 3. 提供数据验证和格式化
 * 4. 支持回退机制
 */

import { InternalDataService, InternalDataResponse, InternalDataError } from './InternalDataService';
import createLogger from '../utils/logger';

const logger = createLogger('DataProcessingService');

/**
 * 查询处理结果接口
 */
export interface QueryProcessingResult {
  processedQuery: string;
  context?: any;
  source: 'internal' | 'direct';
  originalQuery: string;
  timestamp: number;
  internalDataSummary?: string;
}

/**
 * 数据处理配置接口
 */
export interface DataProcessingConfig {
  strictMode: boolean; // 严格模式：必须使用底稿数据
  allowDataModification: boolean; // 是否允许轻微修改数据
  maxDataSize: number; // 最大数据大小限制
  timeoutMs: number; // 处理超时时间
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: DataProcessingConfig = {
  strictMode: true,
  allowDataModification: true, // 允许轻微调整结构和字数和排版
  maxDataSize: 100 * 1024, // 100KB
  timeoutMs: 10000, // 10秒
};

/**
 * 数据处理服务类
 */
export class DataProcessingService {
  private static config: DataProcessingConfig = DEFAULT_CONFIG;

  /**
   * 设置处理配置
   * @param config 新的配置
   */
  static setConfig(config: Partial<DataProcessingConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('[setConfig] 配置已更新:', this.config);
  }

  /**
   * 处理查询请求
   * @param query 原始查询
   * @param useInternalData 是否使用内部数据
   * @param onProgress 进度回调
   * @returns 处理结果
   */
  static async processQuery(
    query: string,
    useInternalData: boolean,
    onProgress?: (progress: number) => void
  ): Promise<QueryProcessingResult> {
    const startTime = Date.now();
    
    logger.info('[processQuery] 开始处理查询:', {
      query: query.substring(0, 50),
      useInternalData,
      timestamp: new Date().toISOString(),
    });

    // 如果不使用内部数据，直接返回原始查询
    if (!useInternalData) {
      onProgress?.(1.0);
      return {
        processedQuery: query,
        source: 'direct',
        originalQuery: query,
        timestamp: Date.now(),
      };
    }

    try {
      onProgress?.(0.1); // 10% - 开始获取底稿数据

      // 获取底稿数据
      const internalDataResponse = await InternalDataService.fetchInternalData(query, true);
      
      onProgress?.(0.5); // 50% - 底稿数据获取完成

      // 检查数据获取是否成功
      if (!internalDataResponse.success) {
        logger.warn('[processQuery] 底稿数据获取失败，回退到直接模式:', {
          query: query.substring(0, 50),
          error: internalDataResponse.error,
        });
        
        onProgress?.(1.0);
        return {
          processedQuery: query,
          source: 'direct',
          originalQuery: query,
          timestamp: Date.now(),
        };
      }

      onProgress?.(0.7); // 70% - 开始构建增强查询

      // 验证数据大小
      const dataSize = JSON.stringify(internalDataResponse.data).length;
      if (dataSize > this.config.maxDataSize) {
        logger.warn('[processQuery] 底稿数据过大，回退到直接模式:', {
          query: query.substring(0, 50),
          dataSize,
          maxSize: this.config.maxDataSize,
        });
        
        onProgress?.(1.0);
        return {
          processedQuery: query,
          source: 'direct',
          originalQuery: query,
          timestamp: Date.now(),
        };
      }

      // 构建增强的查询上下文
      const enhancedQuery = this.buildEnhancedQuery(query, internalDataResponse.data);
      
      onProgress?.(0.9); // 90% - 查询增强完成

      const duration = Date.now() - startTime;
      logger.info('[processQuery] 查询处理完成:', {
        query: query.substring(0, 50),
        source: 'internal',
        duration,
        dataSource: internalDataResponse.source,
      });

      onProgress?.(1.0); // 100% - 完成

      return {
        processedQuery: enhancedQuery,
        context: internalDataResponse.data,
        source: 'internal',
        originalQuery: query,
        timestamp: Date.now(),
        internalDataSummary: internalDataResponse.summary,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('[processQuery] 数据处理失败，回退到直接模式:', {
        query: query.substring(0, 50),
        duration,
        error: error instanceof Error ? error.message : String(error),
      });

      onProgress?.(1.0);
      return {
        processedQuery: query,
        source: 'direct',
        originalQuery: query,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 构建增强的查询prompt
   * @param originalQuery 原始查询
   * @param internalData 底稿数据
   * @returns 增强的查询prompt
   */
  private static buildEnhancedQuery(originalQuery: string, internalData: any): string {
    // 构建严格使用底稿数据的prompt
    const strictDataPrompt = `
🎯 **重要指令：严格使用底稿数据进行UI生成**

用户查询：${originalQuery}

📋 **底稿数据（必须严格遵循）：**
\`\`\`json
${JSON.stringify(internalData, null, 2)}
\`\`\`

🚨 **严格要求：**
1. **数据准确性**：必须严格使用上述底稿数据中的具体内容，不得编造或大幅修改数据
2. **允许的调整**：
   - 可以轻微调整UI结构和布局以提升用户体验
   - 可以适当调整字数和排版以适配移动端显示
   - 可以优化颜色搭配和视觉层次
3. **禁止的操作**：
   - 不得大幅度修改底稿数据中的核心信息
   - 不得添加底稿数据中不存在的内容
   - 不得删除底稿数据中的重要信息

🎨 **UI生成要求：**
- 基于底稿数据创建清晰、美观的移动端界面
- 突出展示底稿数据中的关键信息
- 保持良好的信息层次和视觉平衡
- 确保内容在移动设备上的可读性

请严格按照上述底稿数据生成相应的UI界面，确保数据的准确性和完整性。`;

    return strictDataPrompt;
  }

  /**
   * 验证底稿数据的有效性
   * @param data 底稿数据
   * @returns 是否有效
   */
  private static validateInternalData(data: any): boolean {
    if (!data) {
      logger.warn('[validateInternalData] 底稿数据为空');
      return false;
    }

    // 检查数据类型
    if (typeof data !== 'object') {
      logger.warn('[validateInternalData] 底稿数据类型无效:', typeof data);
      return false;
    }

    // 检查数据大小
    const dataSize = JSON.stringify(data).length;
    if (dataSize > this.config.maxDataSize) {
      logger.warn('[validateInternalData] 底稿数据过大:', { dataSize, maxSize: this.config.maxDataSize });
      return false;
    }

    // 检查是否包含有用信息
    const hasUsefulContent = this.hasUsefulContent(data);
    if (!hasUsefulContent) {
      logger.warn('[validateInternalData] 底稿数据不包含有用信息');
      return false;
    }

    return true;
  }

  /**
   * 检查数据是否包含有用内容
   * @param data 数据对象
   * @returns 是否包含有用内容
   */
  private static hasUsefulContent(data: any): boolean {
    if (typeof data === 'string' && data.trim().length > 0) {
      return true;
    }

    if (Array.isArray(data) && data.length > 0) {
      return data.some(item => this.hasUsefulContent(item));
    }

    if (typeof data === 'object' && data !== null) {
      const values = Object.values(data);
      return values.some(value => this.hasUsefulContent(value));
    }

    return false;
  }

  /**
   * 格式化底稿数据用于显示
   * @param data 底稿数据
   * @returns 格式化后的数据
   */
  static formatInternalDataForDisplay(data: any): string {
    if (!data) return '暂无数据';

    try {
      // 如果是字符串，直接返回
      if (typeof data === 'string') {
        return data.length > 200 ? data.substring(0, 200) + '...' : data;
      }

      // 如果是对象或数组，格式化为JSON
      const jsonString = JSON.stringify(data, null, 2);
      return jsonString.length > 500 ? jsonString.substring(0, 500) + '...' : jsonString;
    } catch (error) {
      logger.error('[formatInternalDataForDisplay] 格式化失败:', error);
      return '数据格式化失败';
    }
  }

  /**
   * 获取处理统计信息
   * @returns 统计信息
   */
  static getProcessingStats(): {
    config: DataProcessingConfig;
    cacheStats: any;
  } {
    return {
      config: this.config,
      cacheStats: InternalDataService.getCacheStats(),
    };
  }
}
