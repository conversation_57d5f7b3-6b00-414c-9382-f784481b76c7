// -----------------------------------------------------------------------------
// EnhancedBatchProcessorService.ts
// -----------------------------------------------------------------------------
// Enhanced batch processing service with advanced concurrency control and monitoring
// 增强版批处理服务，具有高级并发控制和监控功能
//
// Core Features (核心功能):
// 1. Priority Queue System - High-priority task queue jumping (优先级队列系统)
// 2. Dynamic Rate Limiting - Auto-adjust request rates (动态速率限制)
// 3. Intelligent Retry Logic - Exponential backoff with error classification (智能重试逻辑)
// 4. Comprehensive Statistics - Rich performance metrics (全面统计信息)
// 5. Task Grouping - Batch processing by groups (任务分组处理)
// 6. Stream Processing - Real-time data processing (流式数据处理)
// 7. Error Recovery - Automatic error handling and recovery (错误恢复机制)
// -----------------------------------------------------------------------------

import {
  BatchConfig,
  BatchProgress,
  ProcessResult,
  ProcessStatus,
} from '../types';

import { v4 as uuidv4 } from 'uuid';
import {
  parseStreamData,
  extractLynxCode,
  extractHTMLCode,
} from '../utils/streamParser';
// 使用Parse5-based转换系统替代原有的runtime_convert
import { Parse5BatchProcessorAdapter } from '../runtime_convert_parse5/adapters/batch-processor-adapter';
import { UploadService } from './UploadService';
import { LocalStorageService } from './LocalStorageService';
// 🚀 新增：RAG智能增强系统集成
import { ragIntegrationService } from './rag/RAGIntegrationService';
import { RAGConfig } from './rag/interfaces';
// 🚀 新增：底稿数据处理服务
import {
  DataProcessingService,
  QueryProcessingResult,
} from './DataProcessingService';

import {
  ConcurrencyManager,
  JobPriority,
  ConcurrencyManagerStats,
} from '../utils/ConcurrencyManager';
import { errorHandlingService, ErrorType } from './ErrorHandlingService';
import { performanceMonitor } from './PerformanceMonitorService';
import { compressionOptimizer } from './CompressionOptimizationService';
import { toast } from '../../code_generate/utils/toast';

// -----------------------------------------------------------------------------
// Default Configuration Settings
// 默认配置设置 - 可通过构造函数传入 Partial<BatchConfig> 进行覆盖
// Can be overridden by passing Partial<BatchConfig> to constructor
// -----------------------------------------------------------------------------
const DEFAULT_CONFIG: BatchConfig = {
  api: {
    endpoint: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
    workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
    timeout: 180000,
    maxRetries: 3,
    rateLimit: 10, // 每分钟请求上限
  },
  processing: {
    concurrent: 5,
    batchSize: 10,
    delayBetweenRequests: 2000,
    enableCache: true,
    skipExistingFiles: true,
    useInternalData: false, // 默认关闭底稿数据模式
    enableDeduplication: true, // 默认启用查询去重，设为false时允许重复查询
  },
};

// -----------------------------------------------------------------------------
// 增强型任务接口
// -----------------------------------------------------------------------------
interface EnhancedJob {
  id: string;
  query: string;
  priority: JobPriority;
  group?: string;
  weight: number;
  status: ProcessStatus;
  startTime?: number;
  endTime?: number;
  result?: any;
  error?: string;
  retryCount: number;
  maxRetries: number;
  uploadResult?: {
    cdnUrl?: string;
    playgroundUrl?: string;
  };
}

// -----------------------------------------------------------------------------
// 增强型批处理服务
// -----------------------------------------------------------------------------
export class EnhancedBatchProcessorService {
  private config: BatchConfig;
  private concurrencyManager: ConcurrencyManager<ProcessResult>;

  // 事件回调
  private progressCallbacks: Array<(progress: BatchProgress) => void> = [];
  private resultCallbacks: Array<(result: ProcessResult) => void> = [];
  private statsCallbacks: Array<(stats: ConcurrencyManagerStats) => void> = [];

  // 内部状态
  private jobs: Map<string, EnhancedJob> = new Map();
  private isStopped = false;
  private startTime = 0;
  private lastProgressUpdate = 0;
  private progressUpdateInterval = 500; // ms

  // 🔧 新增：重试冷却机制，防止频繁重试
  private lastRetryTime = 0;
  private retryFailedJobsCooldown = 30000; // 30秒冷却时间

  // 服务
  private uploadService = new UploadService({
    enableLogging: true, // 启用日志记录
    mockMode: false, // 确保不处于模拟模式
    timeout: 180000, // 3分钟超时
    maxRetries: 3, // 最大重试次数
    retryInterval: 2000, // 重试间隔
  });
  private systemPrompt: string;

  // Parse5-based Runtime Convert 相关服务
  private batchProcessorAdapter: Parse5BatchProcessorAdapter;

  // 🚀 RAG系统集成
  private ragEnabled = false; // 修复：默认关闭RAG，避免传统模式被污染
  private ragInitialized = false;
  private useTraditionalMode = false; // 新增：明确的传统模式标识

  // 统计数据
  // private successCount = 0;
  // private failureCount = 0;
  private totalProcessingTime = 0;
  private errorTypes: Record<string, number> = {};

  constructor(config?: Partial<BatchConfig>) {
    // 合并配置
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
      api: { ...DEFAULT_CONFIG.api, ...(config?.api ?? {}) },
      processing: {
        ...DEFAULT_CONFIG.processing,
        ...(config?.processing ?? {}),
      },
    } as BatchConfig;

    // 🔧 关键修复：禁用ConcurrencyManager的自动重试，由服务层控制重试逻辑
    this.concurrencyManager = new ConcurrencyManager<ProcessResult>({
      maxConcurrent: this.config.processing.concurrent,
      maxWeight: this.config.processing.concurrent * 2, // 权重上限为并发数的两倍
      rateLimit: {
        requestsPerInterval: this.config.api.rateLimit,
        intervalMs: 60000, // 每分钟
      },
      retryOptions: {
        maxRetries: 0, // 🔧 关键修复：禁用自动重试，避免与服务层重试冲突
        retryDelayMs: this.config.processing.delayBetweenRequests,
        exponentialBackoff: true,
      },
      priorityBoost: true,
      fairScheduling: true,
      queueTimeout: this.config.api.timeout,
    });

    // 从 LocalStorage 加载系统提示词
    this.systemPrompt = LocalStorageService.loadSystemPrompt();

    // 初始化 Parse5-based Runtime Convert 服务
    console.log('🚀 [EnhancedBatchProcessorService] 初始化Parse5转换引擎');
    this.batchProcessorAdapter = new Parse5BatchProcessorAdapter({
      enableCache: true,
      enableScope: true,
      enableOptimization: true,
      strictMode: false, // 宽松模式，提供更好的错误恢复
    });

    console.log('[EnhancedBatchProcessorService] 初始化, 配置:', this.config);
    console.log(
      '[EnhancedBatchProcessorService] 系统提示词长度:',
      this.systemPrompt.length,
    );
    console.log('[EnhancedBatchProcessorService] Parse5转换引擎已初始化');

    // 🚀 初始化RAG系统
    this.initializeRAGSystem();

    // 定期更新进度
    setInterval(
      () => this.updateProgressIfNeeded(),
      this.progressUpdateInterval,
    );
  }

  /**
   * 🚀 初始化RAG系统
   */
  private async initializeRAGSystem(): Promise<void> {
    console.log('[EnhancedBatchProcessorService] 🚀 开始初始化RAG智能增强系统');

    try {
      // RAG系统配置
      const ragConfig: Partial<RAGConfig> = {
        enabled: this.ragEnabled,
        aiAnalysis: {
          endpoint: this.config.api.endpoint,
          timeoutMs: this.config.api.timeout,
          minConfidence: 0.7,
        },
        tokenBudget: {
          maxTokens: 60000,
          reservedTokens: 8000,
          coreTokenLimit: 20000,
          ragTokenLimit: 32000,
        },
        cache: {
          enabled: true,
          ttlMs: 3600000, // 1小时
          maxEntries: 100,
          evictionStrategy: 'lru',
        },
        monitoring: {
          enabled: true,
          intervalMs: 60000,
          detailedLogging: true,
        },
      };

      // 初始化RAG集成服务
      await ragIntegrationService.initialize(ragConfig);
      this.ragInitialized = true;

      console.log(
        '[EnhancedBatchProcessorService] ✅ RAG智能增强系统初始化完成',
      );
      console.log(
        '[EnhancedBatchProcessorService] 🎯 已启用AI驱动的动态提示词增强功能',
      );
    } catch (error) {
      console.error(
        '[EnhancedBatchProcessorService] ❌ RAG系统初始化失败:',
        error,
      );
      console.warn('[EnhancedBatchProcessorService] ⚠️ 降级到传统提示词模式');

      this.ragEnabled = false;
      this.ragInitialized = false;
    }
  }

  /**
   * 🚀 获取智能增强提示词
   * @param query 用户查询
   * @returns 增强后的提示词内容
   */
  private async getEnhancedSystemPrompt(query: string): Promise<string> {
    // 修复：如果明确使用传统模式，直接返回传统提示词
    if (this.useTraditionalMode) {
      console.log(
        '[EnhancedBatchProcessorService] 📋 强制使用传统提示词模式（用户明确选择）',
      );
      return this.systemPrompt;
    }

    if (!this.ragInitialized || !this.ragEnabled) {
      console.log(
        '[EnhancedBatchProcessorService] 📋 使用传统提示词模式（RAG未启用）',
      );
      return this.systemPrompt;
    }

    try {
      console.log(
        `[EnhancedBatchProcessorService] 🧠 为查询生成智能增强提示词: "${query.substring(0, 50)}..."`,
      );

      const startTime = performance.now();

      // 使用传统RAG服务
      let enhancedPrompt: string;
      let enhancementSource = 'Traditional RAG';

      console.log('[EnhancedBatchProcessorService] 🔄 使用传统RAG服务');
      enhancedPrompt = await ragIntegrationService.getEnhancedPrompt(query);

      const enhancementTime = performance.now() - startTime;

      console.log(
        `[EnhancedBatchProcessorService] ✅ ${enhancementSource}增强完成 (${enhancementTime.toFixed(2)}ms)`,
      );
      console.log(
        `[EnhancedBatchProcessorService] 📊 增强提示词长度: ${enhancedPrompt.length} 字符`,
      );

      // 估算token减少
      const originalTokens = Math.ceil(this.systemPrompt.length / 4);
      const enhancedTokens = Math.ceil(enhancedPrompt.length / 4);
      const tokenReduction =
        ((originalTokens - enhancedTokens) / originalTokens) * 100;

      if (tokenReduction > 0) {
        console.log(
          `[EnhancedBatchProcessorService] 📉 Token优化: 减少 ${tokenReduction.toFixed(1)}% (${originalTokens} → ${enhancedTokens})`,
        );
      }

      return enhancedPrompt;
    } catch (error) {
      console.error(
        '[EnhancedBatchProcessorService] ❌ RAG增强失败，使用传统提示词:',
        error,
      );
      return this.systemPrompt;
    }
  }

  /**
   * 设置系统提示词
   */
  setSystemPrompt(prompt: string): void {
    console.log('[EnhancedBatchProcessorService] 更新系统提示词');
    this.systemPrompt = prompt;
    LocalStorageService.saveSystemPrompt(prompt);
  }

  /**
   * 获取系统提示词
   */
  getSystemPrompt(): string {
    return this.systemPrompt;
  }

  /**
   * 🚀 启用/禁用RAG系统
   */
  setRAGEnabled(enabled: boolean): void {
    console.log(
      `[EnhancedBatchProcessorService] 🔧 ${enabled ? '启用' : '禁用'} RAG智能增强系统`,
    );

    this.ragEnabled = enabled;
    // 修复：启用RAG时自动禁用传统模式
    if (enabled) {
      this.useTraditionalMode = false;
    }

    if (this.ragInitialized) {
      ragIntegrationService.setEnabled(enabled);
    }
  }

  /**
   * 🚀 设置传统模式
   */
  setTraditionalMode(enabled: boolean): void {
    console.log(
      `[EnhancedBatchProcessorService] 🔧 ${enabled ? '启用' : '禁用'} 传统模式`,
    );

    this.useTraditionalMode = enabled;
    // 修复：启用传统模式时自动禁用RAG
    if (enabled) {
      this.ragEnabled = false;
      if (this.ragInitialized) {
        ragIntegrationService.setEnabled(false);
      }
    }
  }

  /**
   * 🚀 获取RAG系统状态
   */
  getRAGStatus() {
    return {
      enabled: this.ragEnabled,
      initialized: this.ragInitialized,
      traditionalMode: this.useTraditionalMode,
      health: this.ragInitialized
        ? ragIntegrationService.getHealthStatus()
        : null,
      metrics: this.ragInitialized
        ? ragIntegrationService.getDetailedMetrics()
        : null,
    };
  }

  /**
   * 🚀 测试RAG系统功能
   */
  async testRAGSystem(
    testQuery: string = '创建一个用户数据展示页面',
  ): Promise<any> {
    if (!this.ragInitialized) {
      throw new Error('RAG系统未初始化');
    }

    return ragIntegrationService.testRAGSystem(testQuery);
  }

  /**
   * 🚀 清除RAG缓存
   */
  async clearRAGCache(): Promise<void> {
    if (this.ragInitialized) {
      await ragIntegrationService.clearCache();
      console.log('[EnhancedBatchProcessorService] ✅ RAG缓存已清除');
    }
  }

  // ---------------------------------------------------------------------------
  // Public API
  // ---------------------------------------------------------------------------

  /**
   * 开始批量处理查询
   * @param queries 查询列表
   * @param options 批处理选项
   */
  async startBatch(
    queries: string[],
    options: {
      priority?: JobPriority;
      group?: string;
    } = {},
  ): Promise<string[]> {
    if (queries.length === 0) {
      console.warn('[EnhancedBatchProcessorService] 空查询列表，已忽略');
      return [];
    }

    this.isStopped = false;
    this.startTime = Date.now();

    // 在开始新批处理前清理重复任务
    this.deduplicateJobs();

    // 重置统计
    // this.successCount = 0;
    // this.failureCount = 0;
    this.totalProcessingTime = 0;
    this.errorTypes = {};

    // 创建任务
    const jobIds: string[] = [];
    const priority = options.priority ?? JobPriority.NORMAL;
    const { group } = options;

    // 🔧 根据配置决定是否进行查询去重
    let processQueries: string[];
    let skippedCount = 0;

    if (this.config.processing.enableDeduplication) {
      // 启用去重：避免重复查询，每个查询只创建一个任务
      processQueries = Array.from(new Set(queries));
      skippedCount = queries.length - processQueries.length;

      if (skippedCount > 0) {
        console.warn(
          `[EnhancedBatchProcessorService] 检测到 ${skippedCount} 个重复查询，已自动去重`,
        );

        // 显示toast提示
        try {
          toast.info(`检测到 ${skippedCount} 个重复查询，已自动去重`, {
            key: `batch_dedup_${Date.now()}`,
          });
        } catch (toastError) {
          console.warn(
            '[EnhancedBatchProcessorService] Toast显示失败:',
            toastError,
          );
        }
      }
    } else {
      // 禁用去重：保留所有查询，包括重复的
      processQueries = queries;
      console.log(
        `[EnhancedBatchProcessorService] 去重已禁用，将处理所有 ${queries.length} 个查询（包括重复查询）`,
      );
    }

    // 批量创建任务
    for (const query of processQueries) {
      // 🔧 简化修复：只检查已完成的任务，允许重试失败任务
      const existingSuccessJob = Array.from(this.jobs.values()).find(
        job => job.query === query && job.status === 'success',
      );

      if (existingSuccessJob) {
        console.warn(
          `[EnhancedBatchProcessorService] 跳过重复查询: "${query.substring(0, 30)}..." (已存在成功任务: ${existingSuccessJob.id})`,
        );

        // 如果是成功的任务，直接使用其结果
        jobIds.push(existingSuccessJob.id);
        continue;
      }

      const jobId = uuidv4();
      jobIds.push(jobId);

      const job: EnhancedJob = {
        id: jobId,
        query,
        priority,
        group,
        weight: 1, // 默认权重
        status: 'pending',
        retryCount: 0,
        maxRetries: this.config.api.maxRetries,
      };

      this.jobs.set(jobId, job);
      console.log(
        `[EnhancedBatchProcessorService] 创建新任务: ${jobId} (${query.substring(0, 30)}...)`,
      );
    }

    // 触发初始进度更新
    this.emitProgress();

    // 🔧 关键修复：只为新创建的任务提交到并发管理器
    const newJobs = Array.from(this.jobs.values()).filter(
      job => job.status === 'pending' && jobIds.includes(job.id),
    );

    const processJobs = newJobs.map(
      job => () => this.processQuery(job.query, job.id, priority),
    );

    console.log(
      `[EnhancedBatchProcessorService] 提交 ${processJobs.length} 个新任务到并发管理器`,
    );

    // 批量入队（只提交新任务）
    if (processJobs.length > 0) {
      await this.concurrencyManager.enqueueBatch(processJobs, {
        priority,
        weight: 1,
      });
    }

    return jobIds;
  }

  /**
   * 添加单个高优先级查询
   * 可用于插队处理特定查询
   */
  async addPriorityQuery(
    query: string,
    priority: JobPriority = JobPriority.HIGH,
  ): Promise<string> {
    const jobId = uuidv4();

    const job: EnhancedJob = {
      id: jobId,
      query,
      priority,
      weight: 1,
      status: 'pending',
      retryCount: 0,
      maxRetries: this.config.api.maxRetries,
    };

    this.jobs.set(jobId, job);

    // 入队并立即开始处理
    await this.concurrencyManager.enqueue(
      () => this.processQuery(query, jobId, priority),
      { priority },
    );

    return jobId;
  }

  /**
   * 🎯 设置底稿数据模式
   *
   * 🔧 关键功能：控制是否使用抖音内部底稿数据
   *
   * @param enabled 是否启用底稿数据模式
   *
   * 📋 工作流程：
   * 1. 更新配置中的 useInternalData 标志
   * 2. 记录详细的状态变更日志
   * 3. 验证配置更新是否成功
   *
   * 🚨 重要说明：
   * - 此方法直接影响 processQuery 中的数据源选择逻辑
   * - enabled=true: 先调用底稿数据接口，再调用AI接口
   * - enabled=false: 直接调用AI接口
   *
   * 🐛 常见问题：
   * - 如果开关后仍然直接调用chat接口，检查此方法是否被正确调用
   * - 如果配置被覆盖，检查 updateConfig 方法的调用时机
   */
  setInternalDataMode(enabled: boolean): void {
    const previousState = this.config.processing.useInternalData;

    // 🎯 更新配置
    this.config.processing.useInternalData = enabled;

    // 📝 详细日志记录
    console.log(`[EnhancedBatchProcessorService] 🎛️ 底稿数据模式切换:`);
    console.log(`   📊 前状态: ${previousState ? '启用' : '关闭'}`);
    console.log(`   📊 新状态: ${enabled ? '启用' : '关闭'}`);
    console.log(
      `   ✅ 配置已更新: config.processing.useInternalData = ${this.config.processing.useInternalData}`,
    );

    // 🔍 验证配置更新
    if (this.config.processing.useInternalData !== enabled) {
      console.error(`[EnhancedBatchProcessorService] ❌ 配置更新失败！`);
      console.error(`   预期值: ${enabled}`);
      console.error(`   实际值: ${this.config.processing.useInternalData}`);
    }

    // 🎯 状态变更提示
    if (enabled) {
      console.log(`   🗂️ 下次查询将使用底稿数据模式`);
      console.log(
        `   📞 将先调用: http://9gzj7t9k.fn.bytedance.net/api/search/stream`,
      );
      console.log(`   📞 然后调用: AI接口 (包含底稿数据)`);
    } else {
      console.log(`   🤖 下次查询将使用直接AI模式`);
      console.log(`   📞 将直接调用: AI接口 (原始查询)`);
    }
  }

  /**
   * 获取底稿数据模式状态
   * @returns 是否启用底稿数据模式
   */
  getInternalDataMode(): boolean {
    return this.config.processing.useInternalData;
  }

  /**
   * 停止所有处理
   */
  stopBatch(): number {
    console.log('[EnhancedBatchProcessorService] 停止批处理');
    this.isStopped = true;

    // 取消所有未完成的任务
    const cancelledCount = this.concurrencyManager.cancelAll();

    // 更新任务状态
    for (const job of this.jobs.values()) {
      if (job.status === 'pending' || job.status === 'processing') {
        job.status = 'error';
      }
    }

    // 触发最终进度更新
    this.emitProgress();

    return cancelledCount;
  }

  /**
   * 暂停处理
   */
  pauseProcessing(): void {
    console.log('[EnhancedBatchProcessorService] 暂停处理');
    this.concurrencyManager.pause();
    this.emitProgress();
  }

  /**
   * 恢复处理
   */
  resumeProcessing(): void {
    console.log('[EnhancedBatchProcessorService] 恢复处理');
    this.concurrencyManager.resume();
    this.emitProgress();
  }

  /**
   * 重试失败的任务 - 🔧 简化逻辑，只重试真正的失败任务
   */
  async retryFailedJobs(): Promise<number> {
    console.log('🔄 [EnhancedBatchProcessorService] 开始重试失败任务');

    if (this.isStopped) {
      console.warn('❌ 服务已停止，无法重试');
      return 0;
    }

    // 🔧 修复：检查重试冷却时间，防止频繁重试
    const now = Date.now();
    if (now - this.lastRetryTime < this.retryFailedJobsCooldown) {
      const remainingCooldown =
        this.retryFailedJobsCooldown - (now - this.lastRetryTime);
      console.warn(
        `⚠️ [EnhancedBatchProcessorService] 重试冷却中，还需等待 ${Math.ceil(remainingCooldown / 1000)} 秒`,
      );

      try {
        toast.warning(
          `重试功能冷却中，请等待 ${Math.ceil(remainingCooldown / 1000)} 秒后再试`,
          { key: `retry_cooldown_${now}` },
        );
      } catch (toastError) {
        console.warn(
          '[EnhancedBatchProcessorService] Toast显示失败:',
          toastError,
        );
      }

      return 0;
    }

    this.lastRetryTime = now;

    // 🎯 简化修复：只获取状态为 'error' 的任务，直接过滤
    const allJobs = Array.from(this.jobs.values());

    // 🔧 关键修复：只在源头过滤，避免复杂的多重检查
    const failedJobs = allJobs.filter(job => {
      // 只重试状态为 'error' 的任务
      return job.status === 'error';
    });

    console.log(`📊 发现 ${failedJobs.length} 个状态为 'error' 的任务准备重试`);

    if (failedJobs.length === 0) {
      console.log('ℹ️ 没有状态为 error 的任务需要重试');
      return 0;
    }

    // 🔧 简化重试逻辑：直接重试所有失败任务
    let retryCount = 0;
    for (const job of failedJobs) {
      console.log(`🔄 重试任务: ${job.id} (${job.query.substring(0, 30)}...)`);

      // 重置任务状态
      job.status = 'pending';
      job.retryCount = 0;
      job.error = undefined;
      job.endTime = undefined;

      // 重新入队
      try {
        await this.concurrencyManager.enqueue(
          () => this.processQuery(job.query, job.id, job.priority),
          { priority: job.priority, weight: job.weight },
        );
        retryCount++;
      } catch (error) {
        console.error(`入队失败: ${job.id}`, error);
        job.status = 'error';
        job.error = `入队失败: ${error instanceof Error ? error.message : String(error)}`;
      }
    }

    // 发送进度更新
    this.emitProgress();

    console.log(`✅ 重试完成: ${retryCount} 个失败任务已重新入队`);
    return retryCount;
  }

  /**
   * 获取当前进度状态
   */
  getProgress(): BatchProgress {
    // 🎯 简化修复：直接计算进度，不做复杂的去重检查

    const stats = this.concurrencyManager.getStats();
    const total = this.jobs.size;

    // 直接获取各种状态的作业数量
    const jobsArray = Array.from(this.jobs.values());

    // 🎯 简化修复：基于当前状态直接计算进度
    const succeeded = jobsArray.filter(job => job.status === 'success').length;
    const failed = jobsArray.filter(job => job.status === 'error').length;
    const processing = jobsArray.filter(
      job => job.status === 'processing',
    ).length;
    const pending = total - (succeeded + failed + processing);

    // 计算完成百分比
    const processed = succeeded + failed;
    const percentage =
      total === 0 ? 0 : Math.max(0, Math.min(100, (processed / total) * 100));

    // 计算成功完成百分比
    const successPercentage =
      total === 0 ? 0 : Math.max(0, Math.min(100, (succeeded / total) * 100));

    // 查找当前正在处理的查询
    const activeJobs = jobsArray.filter(job => job.status === 'processing');

    // 计算预计剩余时间
    let estimatedTimeRemaining: number | undefined = undefined;
    if (succeeded > 0 && total > processed) {
      const elapsedTime = Date.now() - this.startTime;
      const averageTimePerJob = elapsedTime / succeeded;
      estimatedTimeRemaining = averageTimePerJob * (total - processed);
    }

    // 更新内部状态计数，确保它们与实际状态保持同步
    // this.successCount = succeeded;
    // this.failureCount = failed;

    return {
      total,
      completed: succeeded, // 成功完成的数量
      failed,
      processing, // 正在处理的任务数量
      pending, // 待处理的任务数量
      percentage,
      successPercentage,
      current: activeJobs.length > 0 ? activeJobs[0].query : undefined,
      throughput: stats.throughput,
      estimatedTimeRemaining,
      startTime: this.startTime,
      activeJobs: stats.activeJobs,
      queuedJobs: stats.queuedJobs,
      averageProcessTime: stats.averageProcessTime,
    } as BatchProgress;
  }

  // 已移除未使用的getDetailedStats()方法

  /**
   * 获取任务状态
   */
  getJobStatus(jobId: string): EnhancedJob | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * 获取所有任务状态
   */
  getAllJobs(): EnhancedJob[] {
    return Array.from(this.jobs.values());
  }

  // 已移除未使用的getJobsByGroup()方法

  /**
   * 注册进度更新回调
   */
  onProgressUpdate(callback: (progress: BatchProgress) => void): void {
    this.progressCallbacks.push(callback);
  }

  /**
   * 注册结果更新回调
   */
  onResultUpdate(callback: (result: ProcessResult) => void): void {
    this.resultCallbacks.push(callback);
  }

  /**
   * 注册统计信息更新回调
   */
  onStatsUpdate(callback: (stats: ConcurrencyManagerStats) => void): void {
    this.statsCallbacks.push(callback);
  }

  /**
   * 更新并发设置
   */
  updateConcurrencySettings(settings: {
    maxConcurrent?: number;
    rateLimit?: number;
    maxRetries?: number;
    delayBetweenRequests?: number;
  }): void {
    if (settings.maxConcurrent) {
      this.config.processing.concurrent = settings.maxConcurrent;
      this.concurrencyManager.updateConfig({
        maxConcurrent: settings.maxConcurrent,
        maxWeight: settings.maxConcurrent * 2,
      });
    }

    if (settings.rateLimit) {
      this.config.api.rateLimit = settings.rateLimit;
      this.concurrencyManager.updateConfig({
        rateLimit: {
          requestsPerInterval: settings.rateLimit,
          intervalMs: 60000,
        },
      });
    }

    if (settings.maxRetries) {
      this.config.api.maxRetries = settings.maxRetries;
      this.concurrencyManager.updateConfig({
        retryOptions: {
          maxRetries: 0, // 🔧 关键修复：始终禁用ConcurrencyManager的自动重试
          retryDelayMs: this.config.processing.delayBetweenRequests,
          exponentialBackoff: true,
        },
      });
    }

    if (settings.delayBetweenRequests) {
      this.config.processing.delayBetweenRequests =
        settings.delayBetweenRequests;
      this.concurrencyManager.updateConfig({
        retryOptions: {
          maxRetries: 0, // 🔧 关键修复：始终禁用ConcurrencyManager的自动重试
          retryDelayMs: settings.delayBetweenRequests,
          exponentialBackoff: true,
        },
      });
    }

    console.log('[EnhancedBatchProcessorService] 更新并发设置:', settings);
  }

  /**
   * 更新完整配置
   * @param config 新的配置
   */
  updateConfig(config: Partial<BatchConfig>): void {
    // 合并配置
    this.config = {
      ...this.config,
      ...config,
      api: { ...this.config.api, ...(config.api ?? {}) },
      processing: {
        ...this.config.processing,
        ...(config.processing ?? {}),
      },
    } as BatchConfig;

    // 更新并发管理器配置
    this.concurrencyManager.updateConfig({
      maxConcurrent: this.config.processing.concurrent,
      maxWeight: this.config.processing.concurrent * 2,
      rateLimit: {
        requestsPerInterval: this.config.api.rateLimit,
        intervalMs: 60000,
      },
      retryOptions: {
        maxRetries: 0, // 🔧 关键修复：始终禁用ConcurrencyManager的自动重试
        retryDelayMs: this.config.processing.delayBetweenRequests,
        exponentialBackoff: true,
      },
      queueTimeout: this.config.api.timeout,
    });

    // 更新上传服务配置
    this.uploadService = new UploadService({
      enableLogging: true,
      mockMode: false,
      timeout: this.config.api.timeout,
      maxRetries: this.config.api.maxRetries,
      retryInterval: this.config.processing.delayBetweenRequests,
    });

    console.log('[EnhancedBatchProcessorService] 更新完整配置:', this.config);
  }

  // ---------------------------------------------------------------------------
  // 私有方法
  // ---------------------------------------------------------------------------

  /**
   * 🔧 关键修复：任务去重和状态清理
   * 清理重复查询的冲突任务，保留最佳状态的任务
   * 根据配置决定是否启用去重功能
   */
  private deduplicateJobs(): void {
    // 如果禁用了去重功能，则跳过任务去重
    if (!this.config.processing.enableDeduplication) {
      console.log(
        '🧹 [EnhancedBatchProcessorService] 任务去重已禁用，跳过去重处理',
      );
      return;
    }

    console.log('🧹 [EnhancedBatchProcessorService] 开始任务去重和状态清理');

    const allJobs = Array.from(this.jobs.values());
    const queryGroups = new Map<string, EnhancedJob[]>();

    // 按查询分组
    for (const job of allJobs) {
      if (!queryGroups.has(job.query)) {
        queryGroups.set(job.query, []);
      }
      queryGroups.get(job.query)!.push(job);
    }

    let cleanedCount = 0;

    // 处理每个查询组
    for (const [query, jobs] of queryGroups) {
      if (jobs.length <= 1) {
        continue; // 无重复，跳过
      }

      console.log(
        `🔍 [EnhancedBatchProcessorService] 发现重复查询: "${query.substring(0, 30)}..." (${jobs.length} 个任务)`,
      );

      // 优先级排序：success > processing > pending > error
      const statusPriority: Record<string, number> = {
        success: 4,
        processing: 3,
        pending: 2,
        error: 1,
        waiting: 0,
      };
      jobs.sort((a, b) => {
        const priorityDiff =
          (statusPriority[b.status] || 0) - (statusPriority[a.status] || 0);
        if (priorityDiff !== 0) {
          return priorityDiff;
        }
        // 相同状态下，保留最新的（ID更大的通常是后创建的）
        return b.id.localeCompare(a.id);
      });

      // 保留第一个（最高优先级的），删除其余的
      const keepJob = jobs[0];
      const removeJobs = jobs.slice(1);

      console.log(
        `✅ [EnhancedBatchProcessorService] 保留任务: ${keepJob.id} (状态: ${keepJob.status})`,
      );

      for (const removeJob of removeJobs) {
        console.log(
          `🗑️ [EnhancedBatchProcessorService] 删除重复任务: ${removeJob.id} (状态: ${removeJob.status})`,
        );
        this.jobs.delete(removeJob.id);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(
        `🎯 [EnhancedBatchProcessorService] 任务去重完成，清理了 ${cleanedCount} 个重复任务`,
      );

      // 显示toast提示
      try {
        toast.success(`任务去重完成，清理了 ${cleanedCount} 个重复任务`, {
          key: `task_cleanup_${Date.now()}`,
        });
      } catch (toastError) {
        console.warn(
          '[EnhancedBatchProcessorService] Toast显示失败:',
          toastError,
        );
      }
    } else {
      console.log('✅ [EnhancedBatchProcessorService] 未发现重复任务');
    }
  }

  /**
   * 处理单个查询
   */
  private async processQuery(
    query: string,
    jobId: string,
    priority: JobPriority,
  ): Promise<ProcessResult> {
    if (this.isStopped) {
      throw new Error('处理已停止');
    }

    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`找不到任务: ${jobId}`);
    }

    // 🔧 根据配置决定是否进行重复任务检查
    if (this.config.processing.enableDeduplication) {
      // 启用去重：基本的重复任务检查，只检查明显冲突
      const allJobs = Array.from(this.jobs.values());
      const existingJob = allJobs.find(
        j =>
          j.query === query &&
          j.id !== jobId &&
          (j.status === 'success' || j.status === 'processing'),
      );

      if (existingJob) {
        const statusText =
          existingJob.status === 'success' ? '已完成' : '正在处理中';
        console.warn(
          `⚠️ [EnhancedBatchProcessorService] 发现同一查询的${statusText}任务，跳过处理: ${query.substring(0, 30)}... (任务ID: ${existingJob.id})`,
        );

        // 如果是成功任务，返回结果；如果是处理中任务，直接跳过
        if (existingJob.status === 'success') {
          // 清理当前任务，避免状态不一致
          this.jobs.delete(jobId);

          // 返回已有的成功结果
          const result: ProcessResult = {
            id: jobId,
            query,
            status: 'success',
            startTime: Date.now(),
            endTime: Date.now(),
            playgroundUrl: existingJob.uploadResult?.playgroundUrl,
            metadata: {
              skipReason: 'duplicate_success_exists',
              existingJobId: existingJob.id,
            },
          };

          this.emitResult(result);
          return result;
        } else {
          // 处理中任务：清理当前任务并抛出错误
          this.jobs.delete(jobId);
          throw new Error(`查询正在被其他任务处理: ${existingJob.id}`);
        }
      }
    } else {
      // 禁用去重：允许处理重复查询，不进行重复检查
      console.log(
        `[EnhancedBatchProcessorService] 去重已禁用，允许处理重复查询: ${query.substring(0, 30)}...`,
      );
    }

    // 开始性能监控
    const performanceId = performanceMonitor.startOperation('process_query', {
      query: query.substring(0, 100),
      jobId,
      priority,
    });

    const start = Date.now();
    console.log(
      `[EnhancedBatchProcessorService] 开始处理: ${query} (优先级: ${priority}, 任务ID: ${jobId})`,
    );

    // 更新任务状态
    job.status = 'processing';
    job.startTime = start;
    this.updateJobAndEmitProgress(job);

    try {
      // 1. 🚀 处理底稿数据（如果启用）
      let processedQuery = query;
      let queryProcessingResult: QueryProcessingResult | null = null;
      let actualContentForAI = query; // 实际传递给AI的内容
      let dataSource: 'internal' | 'ai' | 'fallback' = 'ai'; // 数据源标识
      let internalDataSummary: string | undefined = undefined; // 底稿数据摘要

      // 🔍 详细的配置状态检查和日志
      console.log(`[EnhancedBatchProcessorService] 📊 查询处理配置检查:`);
      console.log(
        `   🎛️ useInternalData: ${this.config.processing.useInternalData}`,
      );
      console.log(
        `   📋 查询内容: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`,
      );

      if (this.config.processing.useInternalData) {
        console.log(`[EnhancedBatchProcessorService] 🗂️ ✅ 底稿数据模式已启用`);
        console.log(
          `   📞 即将调用 DataProcessingService.processQuery(query, true)`,
        );
        console.log(
          `   🌐 这将触发底稿数据接口请求: http://9gzj7t9k.fn.bytedance.net/api/search/stream`,
        );

        try {
          queryProcessingResult = await DataProcessingService.processQuery(
            query,
            true, // 使用内部数据
            progress => {
              console.log(
                `[EnhancedBatchProcessorService] 底稿数据处理进度: ${Math.round(progress * 100)}%`,
              );
            },
          );

          processedQuery = queryProcessingResult.processedQuery;

          // 🎯 关键改造：在底稿数据模式下，传递给AI的content应该是底稿数据的实际内容
          if (
            queryProcessingResult.source === 'internal' &&
            queryProcessingResult.context
          ) {
            // 构建包含底稿数据的完整内容
            actualContentForAI = this.buildInternalDataContent(
              query,
              queryProcessingResult.context,
            );
            dataSource = 'internal'; // 标记为底稿数据源
            internalDataSummary = queryProcessingResult.internalDataSummary;

            console.log(
              '[EnhancedBatchProcessorService] 🎯 使用底稿数据作为AI输入内容',
              {
                originalQuery: query.substring(0, 50),
                internalDataLength: JSON.stringify(
                  queryProcessingResult.context,
                ).length,
                finalContentLength: actualContentForAI.length,
                dataSource: 'internal',
                summary: internalDataSummary,
              },
            );
          } else {
            // 如果底稿数据获取失败，使用原始查询
            actualContentForAI = query;
            dataSource = 'fallback'; // 标记为回退模式
            console.log(
              '[EnhancedBatchProcessorService] ⚠️ 底稿数据不可用，回退到原始查询',
              { dataSource: 'fallback' },
            );
          }

          console.log('[EnhancedBatchProcessorService] ✅ 底稿数据处理完成:', {
            source: queryProcessingResult.source,
            originalQuery: query.substring(0, 50),
            processedLength: processedQuery.length,
            hasContext: !!queryProcessingResult.context,
            actualContentLength: actualContentForAI.length,
          });
        } catch (error) {
          console.warn(
            '[EnhancedBatchProcessorService] ⚠️ 底稿数据处理异常，回退到直接模式:',
            error,
          );
          // 🎯 关键：底稿数据接口出错时，退化到使用原有的query链路
          processedQuery = query;
          actualContentForAI = query;
          dataSource = 'fallback'; // 标记为回退模式
          console.log(
            '[EnhancedBatchProcessorService] 🔄 已退化到原有query链路',
            { dataSource: 'fallback', originalQuery: query.substring(0, 50) },
          );
        }
      } else {
        console.log(`[EnhancedBatchProcessorService] 🤖 ❌ 底稿数据模式已关闭`);
        console.log(`   📝 使用直接AI生成模式`);
        console.log(`   📞 将直接调用 AI接口，不请求底稿数据`);
        console.log(
          `   🌐 跳过底稿数据接口: http://9gzj7t9k.fn.bytedance.net/api/search/stream`,
        );
        actualContentForAI = query;
        dataSource = 'ai'; // 标记为AI直接生成
      }

      // 2. 🚀 获取智能增强提示词（基于原始查询构建系统提示词）
      const enhancedSystemPrompt = await this.getEnhancedSystemPrompt(query);

      // 3. 构建消息（关键：user content使用实际的底稿数据内容）
      const messages = [
        { role: 'system', content: enhancedSystemPrompt },
        { role: 'user', content: actualContentForAI },
      ];

      // 3. 调用AI API
      console.log('[EnhancedBatchProcessorService] 🌐 开始调用AI API');
      const response = await this.callAIApi(messages, query);
      console.log(
        `[EnhancedBatchProcessorService] ✅ AI API调用成功，响应长度: ${response.length} 字符`,
      );

      // 3. 解析流数据
      console.log('[EnhancedBatchProcessorService] 🔍 开始解析流数据');
      console.log(
        `[EnhancedBatchProcessorService] 📊 原始响应长度: ${response.length} 字符`,
      );
      console.log(
        '[EnhancedBatchProcessorService] 📝 原始响应前500字符:',
        response.substring(0, 500),
      );

      const parsedContent = parseStreamData(response);

      if (!parsedContent || parsedContent.length === 0) {
        console.error(
          '[EnhancedBatchProcessorService] ❌ 流数据解析失败：未获取到有效内容',
        );
        console.error(
          `[EnhancedBatchProcessorService] 📊 解析结果: ${parsedContent}`,
        );
        throw new Error('流数据解析失败：未获取到有效内容');
      }

      console.log('[EnhancedBatchProcessorService] ✅ 流数据解析成功');
      console.log(
        `[EnhancedBatchProcessorService] 📊 解析后内容长度: ${parsedContent.length} 字符`,
      );
      console.log(
        '[EnhancedBatchProcessorService] 📝 解析后内容前300字符:',
        parsedContent.substring(0, 300),
      );

      // 4. 简单内容分析 - 快速判断是否为HTML
      console.log('[EnhancedBatchProcessorService] 🔍 简单内容类型检测');
      const isHTML =
        parsedContent.includes('<div') ||
        parsedContent.includes('<html') ||
        parsedContent.includes('<!DOCTYPE') ||
        parsedContent.includes('<head') ||
        parsedContent.includes('<body');

      console.log(
        '[EnhancedBatchProcessorService] 📊 内容类型:',
        isHTML ? 'HTML' : 'LYNX',
      );

      // 5. 根据简单判断选择处理管道
      let extractResult: {
        success: boolean;
        extractedContent?: string;
        error?: string;
        metadata?: any;
      };

      if (isHTML) {
        console.log('[EnhancedBatchProcessorService] 🌐 使用HTML处理管道');
        extractResult = extractHTMLCode(parsedContent) as {
          success: boolean;
          extractedContent?: string;
          error?: string;
          metadata?: any;
        };
        // 如果HTML解析失败，尝试LYNX解析器
        if (!extractResult.success) {
          console.log(
            '[EnhancedBatchProcessorService] 🔄 HTML解析失败，尝试LYNX解析器',
          );
          extractResult = extractLynxCode(parsedContent) as {
            success: boolean;
            extractedContent?: string;
            error?: string;
            metadata?: any;
          };
        }
      } else {
        console.log('[EnhancedBatchProcessorService] 📱 使用LYNX处理管道');
        extractResult = extractLynxCode(parsedContent) as {
          success: boolean;
          extractedContent?: string;
          error?: string;
          metadata?: any;
        };
      }

      console.log('[EnhancedBatchProcessorService] 📊 代码提取详情:', {
        success: extractResult.success,
        hasContent: !!extractResult.extractedContent,
        contentLength: extractResult.extractedContent?.length || 0,
        strategy: extractResult.metadata?.strategy,
        error: extractResult.error,
      });

      if (!extractResult.success) {
        const errorMsg = `代码提取失败: ${extractResult.error || '未知错误'}`;
        console.error(`[EnhancedBatchProcessorService] ❌ ${errorMsg}`);
        console.error(
          '[EnhancedBatchProcessorService] 📊 提取失败详情:',
          extractResult,
        );
        throw new Error(errorMsg);
      }

      if (
        !extractResult.extractedContent ||
        extractResult.extractedContent.length === 0
      ) {
        const contentType = isHTML ? 'HTML' : 'LYNX';
        console.error(
          `[EnhancedBatchProcessorService] ❌ 代码提取失败：未找到有效的${contentType}代码`,
        );
        console.error(
          '[EnhancedBatchProcessorService] 📊 提取结果详情:',
          extractResult,
        );
        throw new Error(`代码提取失败：未找到有效的${contentType}代码`);
      }

      const contentTypeLabel = isHTML ? 'HTML' : 'LYNX';
      console.log(
        `[EnhancedBatchProcessorService] ✅ ${contentTypeLabel}代码提取成功!`,
      );
      console.log(
        `[EnhancedBatchProcessorService] 📊 使用策略: ${extractResult.metadata?.strategy || '未知'}`,
      );
      console.log(
        `[EnhancedBatchProcessorService] 📊 代码块数量: ${extractResult.metadata?.codeBlocks || 0}`,
      );
      console.log(
        `[EnhancedBatchProcessorService] 📏 提取内容长度: ${extractResult.extractedContent.length} 字符`,
      );
      console.log(
        '[EnhancedBatchProcessorService] 📝 提取内容前200字符:',
        extractResult.extractedContent.substring(0, 200),
      );

      // 5. 🔄 真正的数据分叉处理：LYNX先行上传，Runtime Convert并行增强
      // 🔗 LYNX代码的解析、上传、生成Playground URL与AST和Runtime Convert没有任何关系
      // 🔗 不应当因为Runtime Convert被改变生命周期，数据状态在此时是分叉的：
      // 🔗 - 原数据直接上传到Playground URL (独立生命周期，优先执行)
      // 🔗 - Runtime Convert在upload以后才开始使用只读模式进行AST build和convert
      console.log(
        '[EnhancedBatchProcessorService] 🚀 执行真正数据分叉：LYNX先行上传，Runtime Convert并行增强',
      );

      let playgroundUrl = '';
      let uploadResult: any;
      let fileStructure: { [path: string]: string } = {};

      // 🔧 关键修复：保护原始数据，避免被Runtime Convert污染
      const originalContent = extractResult.extractedContent || '';
      const isLynxContent = !isHTML;

      // 🔗 主流程：LYNX数据立即独立上传，不等待Runtime Convert
      if (isLynxContent) {
        console.log(
          '[EnhancedBatchProcessorService] 📱 启动LYNX主流程：立即独立上传（与Runtime Convert完全并行）',
        );

        try {
          // 🔗 构建LYNX文件结构（使用原始数据，不依赖Runtime Convert）
          fileStructure = this.buildLynxFileStructure(originalContent);

          console.log(
            '[EnhancedBatchProcessorService] 📊 LYNX主流程文件结构:',
            {
              fileCount: Object.keys(fileStructure).length,
              files: Object.keys(fileStructure),
            },
          );

          if (Object.keys(fileStructure).length === 0) {
            throw new Error('LYNX文件结构构建失败：未找到有效的文件');
          }

          // 🔗 立即上传原始LYNX代码到CDN，不等待Runtime Convert
          uploadResult = await this.uploadService.uploadToCDN(fileStructure);

          if (!uploadResult?.success) {
            throw new Error(
              `CDN上传失败: ${uploadResult?.error || '未知错误'}`,
            );
          }

          // 🔗 生成Playground URL，让Playground自己解析LYNX代码
          playgroundUrl = this.uploadService.buildPlaygroundUrl(
            uploadResult.cdnUrl,
          );

          if (!playgroundUrl) {
            throw new Error('生成Playground URL失败');
          }

          console.log(
            '[EnhancedBatchProcessorService] ✅ LYNX主流程完成：原始代码已上传',
          );
          console.log(
            `[EnhancedBatchProcessorService] 🔗 Playground URL: ${playgroundUrl}`,
          );

          // 🔗 可选增强：Runtime Convert Worker模式增强（不影响主流程）
          console.log(
            '[EnhancedBatchProcessorService] 🔄 启动Runtime Convert Worker增强（真正后台线程）',
          );

          // ✅ Runtime Convert现在使用Web Worker执行，真正避免阻塞主线程
          this.enhanceWithRuntimeConvertWorker(originalContent, jobId)
            .then(enhanceResult => {
              console.log(
                '[EnhancedBatchProcessorService] ✅ Runtime Convert Worker增强完成:',
                enhanceResult,
              );
            })
            .catch(enhanceError => {
              console.warn(
                '[EnhancedBatchProcessorService] ⚠️ Runtime Convert Worker增强失败（不影响主流程）:',
                enhanceError,
              );
            });
        } catch (lynxUploadError) {
          console.error(
            '[EnhancedBatchProcessorService] ❌ LYNX主流程上传失败:',
            lynxUploadError,
          );
          throw lynxUploadError;
        }
      } else {
        // HTML内容处理：尝试Runtime Convert，失败则直接上传HTML
        console.log(
          '[EnhancedBatchProcessorService] 🌐 HTML内容处理：尝试Runtime Convert增强',
        );

        try {
          // 对HTML内容尝试Runtime Convert增强
          const conversionResult =
            await this.batchProcessorAdapter.convertParsedContent(
              originalContent,
              jobId,
            );

          console.log(
            '[EnhancedBatchProcessorService] 📊 HTML Runtime Convert 转换结果:',
            {
              success: conversionResult.success,
              hasHtml: !!conversionResult.html,
              hasScreenshot: !!conversionResult.screenshot,
              metadata: conversionResult.metadata,
            },
          );

          if (conversionResult.success && conversionResult.html) {
            // HTML内容：使用转换后的HTML直接上传到CDN
            const htmlFileStructure: { [path: string]: string } = {
              'index.html': conversionResult.html,
            };

            console.log(
              '[EnhancedBatchProcessorService] 🌐 HTML转换成功，上传增强HTML到CDN',
            );
            uploadResult = await this.uploadHTMLToCDN(htmlFileStructure);
            playgroundUrl = uploadResult.cdnUrl || '';
            fileStructure = htmlFileStructure;

            console.log(
              '[EnhancedBatchProcessorService] ✅ HTML Runtime Convert 处理完成',
            );
            console.log(
              `[EnhancedBatchProcessorService] 🔗 增强HTML URL: ${playgroundUrl}`,
            );
          } else {
            throw new Error(
              `HTML Runtime Convert转换失败: ${conversionResult.message || '未生成HTML内容'}`,
            );
          }
        } catch (htmlRuntimeConvertError) {
          console.error(
            '[EnhancedBatchProcessorService] ❌ HTML Runtime Convert 处理失败:',
            htmlRuntimeConvertError,
          );

          // HTML fallback: 直接上传原始HTML内容
          console.log(
            '[EnhancedBatchProcessorService] 🔄 HTML fallback：直接上传原始HTML内容',
          );

          const fallbackHtmlStructure: { [path: string]: string } = {
            'index.html': originalContent,
          };

          uploadResult = await this.uploadHTMLToCDN(fallbackHtmlStructure);
          playgroundUrl = uploadResult.cdnUrl || '';
          fileStructure = fallbackHtmlStructure;

          console.log(
            '[EnhancedBatchProcessorService] ✅ HTML fallback上传成功',
          );
          console.log(
            `[EnhancedBatchProcessorService] 🔗 原始HTML URL: ${playgroundUrl}`,
          );
        }
      }

      // 处理完成，继续后续流程

      // 处理完成，继续正常流程（移除了重复的状态检查以避免竞争条件）

      // 正常更新任务状态
      const end = Date.now();
      job.status = 'success';
      job.endTime = end;
      job.uploadResult = {
        cdnUrl: uploadResult.cdnUrl,
        playgroundUrl,
      };

      const processingTime = end - start;

      // 更新统计
      this.totalProcessingTime += processingTime;

      // 不在这里直接累加successCount，由getProgress统一管理计数
      // 删除下面这行，采用getProgress方法的统一计数
      // this.successCount++;

      // 9. 构建结果对象
      const result: ProcessResult = {
        id: jobId,
        query,
        status: 'success',
        startTime: start,
        endTime: end,
        processTime: processingTime,
        playgroundUrl,
        dataSource, // 🎯 添加数据源标识
        internalDataSummary, // 🎯 添加底稿数据摘要（仅当dataSource为internal时有值）
        metadata: {
          extractedContent: extractResult.extractedContent,
          fileCount: Object.keys(fileStructure).length,
          totalSize: Object.values(fileStructure).reduce(
            (sum, content) => sum + content.length,
            0,
          ),
          compressionRatio: uploadResult?.size
            ? Object.values(fileStructure).reduce(
                (sum, content) => sum + content.length,
                0,
              ) / uploadResult.size
            : 1,
          dataSourceInfo: {
            source: dataSource,
            summary: internalDataSummary,
            timestamp: Date.now(),
          },
        },
      };

      // 10. 触发结果回调
      this.emitResult(result);

      // 更新进度
      this.updateJobAndEmitProgress(job);

      // 结束性能监控
      performanceMonitor.endOperation(performanceId, true, {
        processingTime,
        uploadSize: uploadResult?.size || 0,
        playgroundUrl,
      });

      // 返回结果
      return result;
    } catch (error: unknown) {
      // 处理错误
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // 确定错误类型
      let errorType = ErrorType.UNKNOWN_ERROR;
      if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        errorType = ErrorType.TIMEOUT_ERROR;
      } else if (
        errorMessage.includes('网络') ||
        errorMessage.includes('Failed to fetch')
      ) {
        errorType = ErrorType.NETWORK_ERROR;
      } else if (errorMessage.includes('HTTP')) {
        errorType = ErrorType.API_ERROR;
      } else if (
        errorMessage.includes('解析') ||
        errorMessage.includes('提取')
      ) {
        errorType = ErrorType.PARSE_ERROR;
      } else if (errorMessage.includes('上传')) {
        errorType = ErrorType.UPLOAD_ERROR;
      }

      // 记录错误到错误处理服务
      const errorInfo = await errorHandlingService.handleError(
        error instanceof Error ? error : new Error(errorMessage),
        errorType,
        {
          query: query.substring(0, 100),
          jobId,
          priority,
          processingTime: Date.now() - start,
        },
      );

      console.error(
        `[EnhancedBatchProcessorService] 处理失败: ${query}`,
        error,
      );

      // 更新任务状态
      job.status = 'error';
      job.endTime = Date.now();
      job.error = errorMessage;

      this.trackError(errorMessage);

      // 结束性能监控（失败）
      performanceMonitor.endOperation(performanceId, false, {
        errorType,
        errorMessage,
      });

      // 构建错误结果
      const errorResult: ProcessResult = {
        id: jobId,
        query,
        status: 'error',
        startTime: start,
        endTime: Date.now(),
        error: errorMessage,
      };

      // 触发结果回调
      this.emitResult(errorResult);

      // 🔧 关键修复：禁用processQuery中的自动重试，只有显式调用retryFailedJobs才重试
      console.log(
        `[EnhancedBatchProcessorService] 任务失败，等待手动重试: ${query.substring(0, 30)}... (错误: ${errorMessage})`,
      );

      throw error;
    }
  }

  /**
   * 调用AI API - 区分请求超时和流式数据接收超时
   * 实际实现可能需要根据真实API进行调整
   */
  private async callAIApi(messages: any[], query: string): Promise<string> {
    const { endpoint, workflowId, timeout } = this.config.api;
    const requestStartTime = Date.now();

    try {
      // 🔧 修复：延长连接超时到2分钟，给AI处理更多时间
      const connectionTimeout = 120000; // 2分钟
      const connectionController = new AbortController();
      const connectionTimeoutId = setTimeout(() => {
        console.warn(
          `⚠️ [EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 请求连接超过 ${connectionTimeout}ms`,
        );
        connectionController.abort();
      }, connectionTimeout);

      console.log(`[EnhancedBatchProcessorService] 发送API请求到: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId,
          messages,
          stream: true,
        }),
        signal: connectionController.signal,
        // 避免CORS和缓存问题
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'same-origin',
      });

      clearTimeout(connectionTimeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // 第二阶段：流式数据接收（不设置严格超时，但监控数据接收）
      console.log(
        `[EnhancedBatchProcessorService] 开始接收流式数据，查询: "${query.substring(0, 30)}..."`,
      );

      let lastDataReceived = Date.now();
      let receivedData = '';
      let isStreamActive = false;

      // 监控流式数据接收状态
      const streamMonitorInterval = setInterval(() => {
        const now = Date.now();
        const timeSinceLastData = now - lastDataReceived;
        const totalTime = now - requestStartTime;

        // 如果正在接收流式数据，不应该超时
        if (isStreamActive) {
          console.log(
            `🔄 [EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 正在接收流式数据 (已用时: ${totalTime}ms)`,
          );
          return;
        }

        // 如果没有数据流且超时，记录警告但不中断
        if (timeSinceLastData > timeout && totalTime > timeout) {
          console.warn(
            `⚠️ [EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 接口超过 ${timeout}ms，但可能仍在处理中`,
          );
        }
      }, 10000); // 每10秒检查一次

      try {
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('无法获取响应流读取器');
        }

        const decoder = new TextDecoder();

        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log(
              `✅ [EnhancedBatchProcessorService] 流式数据接收完成，查询: "${query.substring(0, 30)}..." (总用时: ${Date.now() - requestStartTime}ms)`,
            );
            break;
          }

          if (value && value.length > 0) {
            lastDataReceived = Date.now();
            isStreamActive = true;
            receivedData += decoder.decode(value, { stream: true });

            // 重置流活跃状态（如果连续1秒没有新数据，认为流暂时不活跃）
            setTimeout(() => {
              isStreamActive = false;
            }, 1000);
          }
        }

        clearInterval(streamMonitorInterval);
        return receivedData;
      } catch (streamError) {
        clearInterval(streamMonitorInterval);
        throw streamError;
      }
    } catch (error: unknown) {
      // 更全面的错误处理
      const err = error as Error;
      const elapsedTime = Date.now() - requestStartTime;

      if (err.name === 'AbortError') {
        // 🔧 修复：调整超时判断，给更长处理时间
        if (elapsedTime < 125000) {
          // 2分5秒
          // 连接阶段超时
          console.error(
            `❌ [EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 连接超时 (${elapsedTime}ms)`,
          );
          throw new Error(`连接超时 (${elapsedTime}ms)`);
        } else {
          console.warn(
            `⚠️ [EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 被中断 (${elapsedTime}ms)，可能不是超时导致`,
          );
          throw new Error(`请求被中断 (${elapsedTime}ms)`);
        }
      } else if (err.message?.includes('port closed')) {
        console.warn(
          `[EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 消息端口已关闭，可能是浏览器网络问题 (${elapsedTime}ms)`,
        );
        throw new Error('网络连接中断: 消息端口已关闭');
      } else if (
        err.name === 'TypeError' &&
        err.message?.includes('Failed to fetch')
      ) {
        console.warn(
          `[EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." 网络请求失败，可能是CORS或网络连接问题 (${elapsedTime}ms)`,
        );
        throw new Error('网络连接错误: 请求失败');
      }

      console.error(
        `❌ [EnhancedBatchProcessorService] 查询 "${query.substring(0, 30)}..." API调用失败 (${elapsedTime}ms):`,
        err.message,
      );
      throw error;
    }
  }

  /**
   * 追踪错误类型
   */
  private trackError(errorMessage: string): void {
    // 简化错误消息以便分类
    let errorType = 'unknown';

    if (errorMessage.includes('timeout')) {
      errorType = 'timeout';
    } else if (errorMessage.includes('HTTP 429')) {
      errorType = 'rate_limit';
    } else if (errorMessage.includes('HTTP 5')) {
      errorType = 'server_error';
    } else if (errorMessage.includes('HTTP 4')) {
      errorType = 'client_error';
    } else if (errorMessage.includes('解析失败')) {
      errorType = 'parsing_error';
    } else if (errorMessage.includes('上传失败')) {
      errorType = 'upload_error';
    }

    this.errorTypes[errorType] = (this.errorTypes[errorType] || 0) + 1;
  }

  /**
   * 更新任务并触发进度更新 - 🔧 增加成功状态保护
   */
  private updateJobAndEmitProgress(job: EnhancedJob): void {
    const existingJob = this.jobs.get(job.id);

    // 🔧 关键保护：如果现有任务已成功，不允许覆盖（除非新状态也是成功）
    if (
      existingJob &&
      existingJob.status === 'success' &&
      job.status !== 'success'
    ) {
      console.warn(
        `[EnhancedBatchProcessorService] 阻止覆盖成功任务: ${job.id} (现有: ${existingJob.status}, 尝试更新为: ${job.status})`,
      );
      return; // 不更新，保持成功状态
    }

    this.jobs.set(job.id, job);
    this.updateProgressIfNeeded(true);
  }

  /**
   * 根据需要更新进度
   */
  private updateProgressIfNeeded(force = false): void {
    const now = Date.now();

    // 如果距离上次更新不足间隔时间且不强制更新，则跳过
    if (!force && now - this.lastProgressUpdate < this.progressUpdateInterval) {
      return;
    }

    this.lastProgressUpdate = now;
    this.emitProgress();

    // 同时更新统计信息
    this.emitStats();
  }

  /**
   * 触发进度更新回调
   */
  private emitProgress(): void {
    // 先计算并缓存进度对象，避免每次回调都重新计算
    const progress = this.getProgress();

    // 缓存回调列表，避免在回调执行过程中修改回调列表导致的问题
    const callbacks = [...this.progressCallbacks];

    // 使用setTimeout避免同步执行可能导致的无限循环
    setTimeout(() => {
      for (const callback of callbacks) {
        try {
          callback(progress);
        } catch (error) {
          console.error('[EnhancedBatchProcessorService] 进度回调错误:', error);
        }
      }
    }, 0);
  }

  /**
   * 触发结果更新回调 - 🔧 增加重复结果保护
   */
  private emitResult(result: ProcessResult): void {
    // 🔧 关键保护：检查是否为重复的成功结果
    const existingJob = this.jobs.get(result.id);
    if (
      existingJob &&
      existingJob.status === 'success' &&
      result.status !== 'success'
    ) {
      console.warn(
        `[EnhancedBatchProcessorService] 阻止发送重复结果: ${result.id} (现有状态: ${existingJob.status}, 尝试发送: ${result.status})`,
      );
      return; // 不发送重复结果
    }

    // 缓存回调列表
    const callbacks = [...this.resultCallbacks];

    // 使用setTimeout避免同步执行可能导致的问题
    setTimeout(() => {
      for (const callback of callbacks) {
        try {
          callback(result);
        } catch (error) {
          console.error('[EnhancedBatchProcessorService] 结果回调错误:', error);
        }
      }
    }, 0);
  }

  /**
   * 触发统计信息更新回调
   */
  private emitStats(): void {
    const stats = this.concurrencyManager.getStats();

    // 缓存回调列表
    const callbacks = [...this.statsCallbacks];

    // 使用setTimeout避免同步执行可能导致的问题
    setTimeout(() => {
      for (const callback of callbacks) {
        try {
          callback(stats);
        } catch (error) {
          console.error('[EnhancedBatchProcessorService] 统计回调错误:', error);
        }
      }
    }, 0);
  }

  /**
   * HTML内容上传到CDN
   * HTML内容直接压缩上传到CDN，不经过Playground
   */
  private async uploadHTMLToCDN(
    fileStructure: { [path: string]: string },
    // job: EnhancedJob,
  ): Promise<{ cdnUrl: string; downloadUrl: string }> {
    console.log('[EnhancedBatchProcessorService] 🌐 开始HTML CDN上传处理');

    try {
      // 1. 验证HTML文件结构
      const htmlFiles = Object.entries(fileStructure).filter(
        ([path]) => path.endsWith('.html') || path.endsWith('.htm'),
      );

      if (htmlFiles.length === 0) {
        throw new Error('未找到HTML文件');
      }

      // 2. 创建压缩包
      console.log('[EnhancedBatchProcessorService] 📦 创建HTML压缩包');

      // 计算总大小
      const totalSize = Object.values(fileStructure).reduce(
        (sum, content) => sum + content.length,
        0,
      );

      console.log(
        `[EnhancedBatchProcessorService] 📊 HTML内容总大小: ${totalSize} 字符`,
      );

      // 3. 压缩优化
      const compressionResult = await compressionOptimizer.compressAndOptimize(
        fileStructure,
        {
          enableMinification: false, // HTML保持原始格式
          preserveComments: true,
          preserveWhitespace: true,
          compressionLevel: 'fast', // 快速压缩
        },
      );

      if (!compressionResult.success) {
        console.warn(
          '[EnhancedBatchProcessorService] ⚠️ HTML压缩失败，使用原始内容',
        );
      }

      const finalFileStructure = compressionResult.success
        ? compressionResult.optimizedFiles
        : fileStructure;

      // 4. 上传到CDN
      console.log('[EnhancedBatchProcessorService] ☁️ 开始CDN上传');

      const uploadResult =
        await this.uploadService.uploadToCDN(finalFileStructure);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'CDN上传失败');
      }

      console.log('[EnhancedBatchProcessorService] ✅ HTML CDN上传成功');
      console.log(
        `[EnhancedBatchProcessorService] 🔗 CDN URL: ${uploadResult.cdnUrl}`,
      );

      // 5. 生成下载链接
      const downloadUrl = uploadResult.cdnUrl || uploadResult.downloadUrl || '';

      return {
        cdnUrl: uploadResult.cdnUrl || '',
        downloadUrl,
      };
    } catch (error) {
      console.error(
        '[EnhancedBatchProcessorService] ❌ HTML CDN上传失败:',
        error,
      );

      // 错误分类和处理
      if (error instanceof Error) {
        if (error.message.includes('网络')) {
          throw new Error('网络连接问题，CDN上传失败');
        } else if (error.message.includes('大小')) {
          throw new Error('HTML内容过大，无法上传到CDN');
        } else if (error.message.includes('格式')) {
          throw new Error('HTML文件格式不正确');
        }
      }

      throw new Error(
        `HTML CDN上传失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 构建LYNX文件结构用于Playground
   * 🔧 关键修复：从提取的内容构建符合LYNX项目结构的文件映射
   * @param extractedContent 从AI响应中提取的LYNX代码
   * @returns 文件路径到内容的映射
   */
  private buildLynxFileStructure(extractedContent: string): {
    [path: string]: string;
  } {
    console.log('[EnhancedBatchProcessorService] 🔨 开始构建LYNX文件结构');

    const fileStructure: { [path: string]: string } = {};

    try {
      // 尝试解析包含多文件的结构
      // 🔧 修复：同时检查 <FILES> 包装器和 <FILE> 标签
      if (
        extractedContent.includes('<FILE') &&
        extractedContent.includes('</FILE>')
      ) {
        console.log(
          '[EnhancedBatchProcessorService] 📄 检测到多文件结构，开始解析',
        );

        // 匹配所有文件标签，支持both path= and name= 属性
        const filePattern =
          /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g;
        let match;
        let fileCount = 0;

        while ((match = filePattern.exec(extractedContent)) !== null) {
          const [fullMatch, , fileContent] = match;
          const trimmedContent = fileContent.trim();

          if (trimmedContent) {
            // 🔧 修复：优先匹配 path 属性，如果没有则匹配 name 属性
            let pathMatch = fullMatch.match(/path="([^"]+)"/);
            if (!pathMatch) {
              pathMatch = fullMatch.match(/name="([^"]+)"/);
            }

            if (pathMatch) {
              const originalPath = pathMatch[1];
              fileStructure[originalPath] = trimmedContent;
              fileCount++;

              console.log(
                `[EnhancedBatchProcessorService] ✅ 解析文件: ${originalPath} (${trimmedContent.length} 字符)`,
              );
            }
          }
        }

        console.log(
          `[EnhancedBatchProcessorService] 📊 解析完成，共 ${fileCount} 个文件`,
        );
      } else {
        console.log(
          '[EnhancedBatchProcessorService] 📄 检测到单文件LYNX代码，创建默认结构',
        );

        // 单文件情况：创建标准的LYNX项目结构
        fileStructure['src/index.ttml'] = extractedContent;
      }

      // 确保有主入口文件
      if (
        !fileStructure['src/index.ttml'] &&
        !Object.keys(fileStructure).some(path => path.endsWith('.ttml'))
      ) {
        console.warn(
          '[EnhancedBatchProcessorService] ⚠️ 未找到TTML文件，将extractedContent作为index.ttml',
        );
        fileStructure['src/index.ttml'] = extractedContent;
      }

      console.log('[EnhancedBatchProcessorService] ✅ LYNX文件结构构建完成:', {
        fileCount: Object.keys(fileStructure).length,
        files: Object.keys(fileStructure),
        totalSize: Object.values(fileStructure).reduce(
          (sum, content) => sum + content.length,
          0,
        ),
      });

      // 🔧 重要修复：直接返回基础文件结构，让 UploadService.compressFiles() 添加配置文件
      // UploadService 已有完整的配置模板生成逻辑，包括 detectMainEntryPath 和 getConfigTemplates
      // 避免重复实现相同的功能
      return fileStructure;
    } catch (error) {
      console.error(
        '[EnhancedBatchProcessorService] ❌ 构建LYNX文件结构失败:',
        error,
      );

      // 降级处理：创建最简单的结构
      return {
        'src/index.ttml': extractedContent,
      };
    }
  }

  /**
   * Runtime Convert Worker模式增强处理（真正后台线程）
   * ✅ Runtime Convert现在运行在Web Worker中，真正避免阻塞主线程
   * 🔗 关键架构：使用Web Worker执行重型计算，不影响主要上传流程
   * @param originalContent 原始LYNX内容
   * @param jobId 任务ID
   * @returns Promise<any> 增强处理结果
   */
  private async enhanceWithRuntimeConvertWorker(
    originalContent: string,
    jobId: string,
  ): Promise<any> {
    console.log(
      '[EnhancedBatchProcessorService] 🔄 Runtime Convert Worker增强开始（Web Worker模式）',
    );
    console.log(
      '[EnhancedBatchProcessorService] ✅ Runtime Convert现在运行在Web Worker中，真正后台处理',
    );

    try {
      // 🔗 Worker处理策略：重型计算在Web Worker中执行，主线程完全不被阻塞
      const startTime = performance.now();

      // 🔗 使用只读模式处理原始内容，不影响已上传的数据
      const enhanceResult =
        await this.batchProcessorAdapter.convertParsedContent(
          originalContent,
          jobId,
        );

      const processingTime = performance.now() - startTime;

      console.log(
        '[EnhancedBatchProcessorService] 📊 Runtime Convert Worker增强结果:',
        {
          success: enhanceResult.success,
          hasHtml: !!enhanceResult.html,
          hasScreenshot: !!enhanceResult.screenshot,
          metadata: enhanceResult.metadata,
          processingTime: `${processingTime.toFixed(2)}ms`,
          threadType: 'Web Worker',
        },
      );

      // Worker模式下，主线程完全不被阻塞，可以处理更大的文件
      console.log(
        `[EnhancedBatchProcessorService] ✅ Worker处理耗时: ${processingTime.toFixed(2)}ms，主线程未被阻塞`,
      );

      // 🔗 Worker增强结果可用于：
      // - 生成预览截图
      // - 创建Web版本预览
      // - 性能分析
      // - 代码质量检查
      // 但不会影响已经上传的原始LYNX代码和Playground链接

      return {
        success: enhanceResult.success,
        html: enhanceResult.html,
        screenshot: enhanceResult.screenshot,
        metadata: enhanceResult.metadata,
        processingTime: `${processingTime.toFixed(2)}ms`,
        threadType: 'Web Worker',
        note: 'Runtime Convert Worker增强完成，主线程未被阻塞',
      };
    } catch (error) {
      console.warn(
        '[EnhancedBatchProcessorService] ⚠️ Runtime Convert Worker增强失败（不影响主流程）:',
        error,
      );

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        threadType: 'Web Worker',
        note: 'Runtime Convert Worker增强失败，主流程不受影响',
      };
    }
  }

  /**
   * Runtime Convert分片增强处理（避免主线程阻塞）
   * @deprecated 已迁移到Web Worker模式，使用 enhanceWithRuntimeConvertWorker 替代
   * @param originalContent 原始LYNX内容
   * @param jobId 任务ID
   * @returns Promise<any> 增强处理结果
   */
  // private async enhanceWithRuntimeConvertChunked(
  //   originalContent: string,
  //   jobId: string,
  // ): Promise<any> {
  //   // 重定向到Worker模式
  //   return this.enhanceWithRuntimeConvertWorker(originalContent, jobId);
  // }

  /**
   * Runtime Convert后台增强处理（只读模式）
   * @deprecated 使用 enhanceWithRuntimeConvertChunked 替代，该方法更准确反映实际执行环境
   * 🔗 关键架构：这个方法在LYNX主流程完成后异步执行，不影响主要上传流程
   * @param originalContent 原始LYNX内容
   * @param jobId 任务ID
   * @returns Promise<any> 增强处理结果
   */
  // private async enhanceWithRuntimeConvert(
  //   originalContent: string,
  //   jobId: string,
  // ): Promise<any> {
  //   // 重定向到分片处理方法
  //   return this.enhanceWithRuntimeConvertChunked(originalContent, jobId);
  // }

  /**
   * 构建包含底稿数据的AI输入内容
   *
   * 🎯 核心功能：
   * - 将底稿数据的实际内容作为AI的输入
   * - 确保AI基于权威数据进行生成
   * - 保持原始查询的上下文信息
   *
   * @param originalQuery 用户的原始查询
   * @param internalData 底稿数据接口返回的数据
   * @returns 构建好的AI输入内容
   */
  private buildInternalDataContent(
    originalQuery: string,
    internalData: any,
  ): string {
    try {
      // 构建包含底稿数据的完整内容
      const content = `用户查询：${originalQuery}

请基于以下抖音内部底稿数据生成UI界面：

【底稿数据内容】
${internalData.answer || ''}

【数据热度】
${internalData.pv ? `${internalData.pv} 次浏览` : ''}

【参考资料】
${internalData.reference || ''}

${internalData.reasoning ? `【推理过程】\n${internalData.reasoning}` : ''}

请严格基于以上底稿数据内容生成相应的UI界面，确保信息的准确性和权威性。`;

      console.log('[EnhancedBatchProcessorService] 构建底稿数据内容完成:', {
        originalQueryLength: originalQuery.length,
        answerLength: (internalData.answer || '').length,
        referenceLength: (internalData.reference || '').length,
        finalContentLength: content.length,
      });

      return content;
    } catch (error) {
      console.error(
        '[EnhancedBatchProcessorService] 构建底稿数据内容失败:',
        error,
      );
      // 如果构建失败，返回原始查询
      return originalQuery;
    }
  }

  /**
   * 清理缓存数据
   */
  private clearCache(): void {
    // 清理内存缓存
    this.jobs.clear();
    this.errorTypes = {};
    console.log('[EnhancedBatchProcessorService] 缓存已清理');
  }

  /**
   * 销毁实例，清理所有资源
   */
  async dispose(): Promise<void> {
    console.log('[EnhancedBatchProcessorService] 开始销毁实例');

    // 清理缓存
    this.clearCache();

    // 🚀 清理RAG系统
    if (this.ragInitialized) {
      try {
        await ragIntegrationService.dispose();
        console.log('[EnhancedBatchProcessorService] RAG智能增强系统已清理');
      } catch (error) {
        console.warn(
          '[EnhancedBatchProcessorService] RAG系统清理时出现警告:',
          error,
        );
      }
    }

    // 清理 Parse5-based Runtime Convert 相关服务
    try {
      this.batchProcessorAdapter?.dispose();
      console.log(
        '[EnhancedBatchProcessorService] Parse5 Runtime Convert 服务已清理',
      );
    } catch (error) {
      console.warn(
        '[EnhancedBatchProcessorService] Parse5 Runtime Convert 服务清理时出现警告:',
        error,
      );
    }

    // 停止所有处理
    this.stopBatch();

    console.log('[EnhancedBatchProcessorService] 实例已销毁');
  }
}
