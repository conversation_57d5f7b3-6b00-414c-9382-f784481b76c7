/**
 * 模式切换组件
 * 
 * 功能：
 * 1. 提供"使用抖音内部的底稿数据"开关
 * 2. 显示当前模式状态
 * 3. 提供用户友好的交互体验
 * 4. 支持禁用状态和加载状态
 */

import React, { useCallback } from 'react';
import { Switch } from '@douyinfe/semi-ui';
import { Icon } from '../components/Icon';

/**
 * 模式切换组件属性接口
 */
export interface ModeToggleProps {
  /** 是否使用内部数据 */
  useInternalData: boolean;
  /** 切换回调函数 */
  onToggle: (enabled: boolean) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否正在加载 */
  loading?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 显示详细描述 */
  showDescription?: boolean;
}

/**
 * 数据源指示器组件
 */
const DataSourceIndicator: React.FC<{
  useInternalData: boolean;
  loading: boolean;
}> = ({ useInternalData, loading }) => {
  if (loading) {
    return (
      <div className="flex items-center text-xs text-blue-600">
        <div className="animate-spin mr-1">
          <Icon type="loading" size="xs" />
        </div>
        <span>正在获取底稿数据...</span>
      </div>
    );
  }

  if (useInternalData) {
    return (
      <div className="flex items-center text-xs text-blue-600">
        <Icon type="database" size="xs" className="mr-1" />
        <span>底稿数据模式</span>
      </div>
    );
  }

  return (
    <div className="flex items-center text-xs text-gray-500">
      <Icon type="zap" size="xs" className="mr-1" />
      <span>直接AI生成模式</span>
    </div>
  );
};

/**
 * 模式切换组件
 */
export const ModeToggle: React.FC<ModeToggleProps> = ({
  useInternalData,
  onToggle,
  disabled = false,
  loading = false,
  className = '',
  showDescription = true,
}) => {
  // 处理切换事件
  const handleToggle = useCallback((checked: boolean) => {
    if (disabled || loading) {
      return;
    }
    
    console.log(`[ModeToggle] 数据源模式切换: ${checked ? '内部数据' : '直接模式'}`);
    onToggle(checked);
  }, [onToggle, disabled, loading]);

  // 获取描述文本
  const getDescriptionText = () => {
    if (loading) {
      return "正在获取底稿数据，请稍候...";
    }
    
    if (useInternalData) {
      return "已启用：将使用抖音内部底稿数据作为数据源，确保信息准确性";
    }
    
    return "已关闭：直接使用AI生成内容，响应更快但可能缺乏特定数据";
  };

  return (
    <div className={`mode-toggle-container ${className}`}>
      {/* 主要切换区域 */}
      <div className="mode-toggle-content">
        <div className="mode-toggle-left">
          <div className="mode-toggle-label-wrapper">
            <span className="mode-toggle-label">
              使用抖音内部的底稿数据
            </span>
            {/* 数据源指示器 */}
            <DataSourceIndicator 
              useInternalData={useInternalData} 
              loading={loading} 
            />
          </div>
        </div>
        
        <div className="mode-toggle-right">
          <Switch
            checked={useInternalData}
            onChange={handleToggle}
            size="small"
            disabled={disabled || loading}
            className="mode-toggle-switch"
            aria-label="切换数据源模式"
          />
        </div>
      </div>

      {/* 描述文本 */}
      {showDescription && (
        <div className="mode-toggle-description">
          {getDescriptionText()}
        </div>
      )}

      {/* 功能说明 */}
      {useInternalData && !loading && (
        <div className="mode-toggle-features">
          <div className="mode-toggle-feature-item">
            <Icon type="check-circle" size="xs" className="text-green-500" />
            <span>数据准确性更高</span>
          </div>
          <div className="mode-toggle-feature-item">
            <Icon type="check-circle" size="xs" className="text-green-500" />
            <span>内容更加权威</span>
          </div>
          <div className="mode-toggle-feature-item">
            <Icon type="info-circle" size="xs" className="text-blue-500" />
            <span>响应时间稍长</span>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * 简化版模式切换组件（用于空间受限的场景）
 */
export const CompactModeToggle: React.FC<Omit<ModeToggleProps, 'showDescription'>> = ({
  useInternalData,
  onToggle,
  disabled = false,
  loading = false,
  className = '',
}) => {
  const handleToggle = useCallback((checked: boolean) => {
    if (disabled || loading) {
      return;
    }
    onToggle(checked);
  }, [onToggle, disabled, loading]);

  return (
    <div className={`compact-mode-toggle ${className}`}>
      <div className="compact-mode-toggle-content">
        <span className="compact-mode-toggle-label">
          底稿数据
        </span>
        <Switch
          checked={useInternalData}
          onChange={handleToggle}
          size="small"
          disabled={disabled || loading}
          className="compact-mode-toggle-switch"
          aria-label="切换数据源模式"
        />
      </div>
      
      {loading && (
        <div className="compact-mode-toggle-loading">
          <Icon type="loading" size="xs" className="animate-spin" />
        </div>
      )}
    </div>
  );
};

export default ModeToggle;
