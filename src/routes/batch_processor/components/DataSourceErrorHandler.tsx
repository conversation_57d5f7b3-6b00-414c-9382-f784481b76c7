/**
 * 数据源错误处理组件
 * 
 * 功能：
 * 1. 显示底稿数据获取错误
 * 2. 提供用户友好的错误信息
 * 3. 支持重试和回退操作
 * 4. 显示错误统计信息
 */

import React, { useCallback } from 'react';
import { Icon } from './Icon';
import { InternalDataError } from '../services/InternalDataService';

/**
 * 错误处理组件属性接口
 */
export interface DataSourceErrorHandlerProps {
  /** 错误类型 */
  error: InternalDataError | null;
  /** 错误消息 */
  errorMessage?: string;
  /** 查询内容 */
  query?: string;
  /** 重试回调 */
  onRetry?: () => void;
  /** 回退到直接模式回调 */
  onFallback?: () => void;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 错误信息映射
 */
const ERROR_MESSAGES = {
  [InternalDataError.NETWORK_ERROR]: {
    title: '网络连接失败',
    description: '无法连接到底稿数据服务器，请检查网络连接',
    icon: 'wifi-off',
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
  },
  [InternalDataError.TIMEOUT_ERROR]: {
    title: '请求超时',
    description: '底稿数据获取超时，服务器响应较慢',
    icon: 'clock',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
  },
  [InternalDataError.PARSE_ERROR]: {
    title: '数据解析失败',
    description: '底稿数据格式异常，无法正确解析',
    icon: 'alert-triangle',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
  },
  [InternalDataError.API_ERROR]: {
    title: '接口调用失败',
    description: '底稿数据接口返回错误，请稍后重试',
    icon: 'server',
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
  },
  [InternalDataError.INVALID_QUERY]: {
    title: '查询参数无效',
    description: '查询内容为空或格式不正确',
    icon: 'edit-3',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
  },
};

/**
 * 数据源错误处理组件
 */
export const DataSourceErrorHandler: React.FC<DataSourceErrorHandlerProps> = ({
  error,
  errorMessage,
  query,
  onRetry,
  onFallback,
  showDetails = false,
  className = '',
}) => {
  // 如果没有错误，不显示组件
  if (!error) {
    return null;
  }

  const errorInfo = ERROR_MESSAGES[error];
  
  const handleRetry = useCallback(() => {
    if (onRetry) {
      console.log('[DataSourceErrorHandler] 用户点击重试');
      onRetry();
    }
  }, [onRetry]);

  const handleFallback = useCallback(() => {
    if (onFallback) {
      console.log('[DataSourceErrorHandler] 用户选择回退到直接模式');
      onFallback();
    }
  }, [onFallback]);

  return (
    <div className={`data-source-error-handler ${className}`}>
      <div className={`error-container ${errorInfo.bgColor} ${errorInfo.borderColor}`}>
        {/* 错误标题 */}
        <div className="error-header">
          <div className="error-icon-wrapper">
            <Icon 
              type={errorInfo.icon as any} 
              size="sm" 
              className={errorInfo.color}
            />
          </div>
          <div className="error-title-wrapper">
            <h4 className={`error-title ${errorInfo.color}`}>
              {errorInfo.title}
            </h4>
            <p className="error-description">
              {errorInfo.description}
            </p>
          </div>
        </div>

        {/* 详细错误信息 */}
        {showDetails && (errorMessage || query) && (
          <div className="error-details">
            {query && (
              <div className="error-detail-item">
                <span className="error-detail-label">查询内容:</span>
                <span className="error-detail-value">
                  {query.length > 50 ? `${query.substring(0, 50)}...` : query}
                </span>
              </div>
            )}
            {errorMessage && (
              <div className="error-detail-item">
                <span className="error-detail-label">错误详情:</span>
                <span className="error-detail-value">{errorMessage}</span>
              </div>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="error-actions">
          {onRetry && (
            <button
              onClick={handleRetry}
              className="error-action-btn error-action-retry"
            >
              <Icon type="refresh-cw" size="xs" className="mr-1" />
              重试获取
            </button>
          )}
          {onFallback && (
            <button
              onClick={handleFallback}
              className="error-action-btn error-action-fallback"
            >
              <Icon type="zap" size="xs" className="mr-1" />
              使用直接模式
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * 简化版错误提示组件
 */
export const CompactErrorHandler: React.FC<{
  error: InternalDataError;
  onDismiss?: () => void;
}> = ({ error, onDismiss }) => {
  const errorInfo = ERROR_MESSAGES[error];

  return (
    <div className="compact-error-handler">
      <div className={`compact-error-content ${errorInfo.bgColor} ${errorInfo.borderColor}`}>
        <Icon 
          type={errorInfo.icon as any} 
          size="xs" 
          className={errorInfo.color}
        />
        <span className="compact-error-text">
          {errorInfo.title}
        </span>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="compact-error-dismiss"
          >
            <Icon type="x" size="xs" />
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * 错误统计组件
 */
export const ErrorStatsDisplay: React.FC<{
  stats: {
    totalErrors: number;
    networkErrors: number;
    timeoutErrors: number;
    parseErrors: number;
    apiErrors: number;
  };
}> = ({ stats }) => {
  if (stats.totalErrors === 0) {
    return null;
  }

  return (
    <div className="error-stats-display">
      <div className="error-stats-header">
        <Icon type="alert-circle" size="sm" className="text-orange-500" />
        <span className="error-stats-title">错误统计</span>
      </div>
      <div className="error-stats-grid">
        <div className="error-stat-item">
          <span className="error-stat-label">总错误数</span>
          <span className="error-stat-value">{stats.totalErrors}</span>
        </div>
        <div className="error-stat-item">
          <span className="error-stat-label">网络错误</span>
          <span className="error-stat-value">{stats.networkErrors}</span>
        </div>
        <div className="error-stat-item">
          <span className="error-stat-label">超时错误</span>
          <span className="error-stat-value">{stats.timeoutErrors}</span>
        </div>
        <div className="error-stat-item">
          <span className="error-stat-label">解析错误</span>
          <span className="error-stat-value">{stats.parseErrors}</span>
        </div>
        <div className="error-stat-item">
          <span className="error-stat-label">接口错误</span>
          <span className="error-stat-value">{stats.apiErrors}</span>
        </div>
      </div>
    </div>
  );
};

export default DataSourceErrorHandler;
