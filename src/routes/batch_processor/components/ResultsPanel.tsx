import React, { useMemo, useState } from 'react';
import { ProcessResult, BatchProgress } from '../types';
import Tooltip from './Tooltip';
import Icon from './Icon';
import { colors } from '../config/theme';
// AutoWebPreview 已整合到 InteractiveIframe 中，列表视图已移除
import { ResultCardsGrid } from './card-view';
import { DataSourceLabel, DataSourceStats } from './DataSourceLabel';

// -----------------------------------------------------------------------------
// ResultsPanel
// -----------------------------------------------------------------------------
// 该组件将批量处理结果渲染为列表。每个列表项包含：
//   - 状态图标 (成功 ✅ / 处理中 ⏳ / 失败 ❌)
//   - 查询文本
//   - Playground URL 链接（若成功）
//   - 错误信息（若失败）
// 在后续迭代中，该组件可以进一步拆分为更小的 ResultItem，以便进行虚拟化
// 渲染或复杂交互。
// -----------------------------------------------------------------------------

interface ResultsPanelProps {
  /** 处理结果数组 */
  results: ProcessResult[];
  /** 批处理进度信息 */
  progress?: BatchProgress;
  /** 是否正在运行批处理 */
  isRunning?: boolean;
  /** 重试失败项的函数 */
  onRetryFailed?: () => Promise<void>;
  /** 是否正在重试中 */
  isRetrying?: boolean;
}

export const ResultsPanel: React.FC<ResultsPanelProps> = ({
  results,
  progress,
  isRunning = false,
  onRetryFailed,
  isRetrying = false,
}) => {
  // 状态过滤
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  // 复制状态管理
  const [copySuccess, setCopySuccess] = useState(false);
  // 视图模式：list（列表视图）或 card（卡片视图）
  const [viewMode, setViewMode] = useState<'list' | 'card'>('list');
  // 卡片视图状态
  const [focusedCardId, setFocusedCardId] = useState<string | null>(null);
  const [selectedCardIds, setSelectedCardIds] = useState<string[]>([]);

  // 根据状态过滤结果
  const filteredResults = useMemo(() => {
    if (!statusFilter) {
      return results;
    }
    return results.filter(result => result.status === statusFilter);
  }, [results, statusFilter]);

  // 计算数据源统计
  const dataSourceStats = useMemo(() => {
    const stats = {
      internal: 0,
      ai: 0,
      fallback: 0,
    };

    results.forEach(result => {
      if (result.dataSource) {
        stats[result.dataSource]++;
      } else {
        // 兼容旧数据，默认为AI生成
        stats.ai++;
      }
    });

    return stats;
  }, [results]);

  // 统计不同状态的结果数量 - 🔧 修复数据不一致问题：统一使用progress数据源
  const stats = useMemo(() => {
    // 优先使用progress数据，确保与右侧状态概览一致
    if (progress) {
      return {
        success: progress.completed,
        error: progress.failed,
        processing: progress.processing,
        pending: progress.pending,
      };
    }

    // 降级方案：如果没有progress数据，则从results计算
    const success = results.filter(r => r.status === 'success').length;
    const error = results.filter(r => r.status === 'error').length;
    const processing = results.filter(r => r.status === 'processing').length;
    const pending = results.length - (success + error + processing);

    return { success, error, processing, pending };
  }, [results, progress]);

  // 卡片视图相关回调
  const handleCardFocus = (cardId: string | null) => {
    setFocusedCardId(cardId);
  };

  const handleCardSelection = (cardId: string, selected: boolean) => {
    setSelectedCardIds(prev => {
      if (selected) {
        return [...prev, cardId];
      } else {
        return prev.filter(id => id !== cardId);
      }
    });
  };

  const handleBatchAction = async (action: string, cardIds: string[]) => {
    switch (action) {
      case 'openAll':
        {
          // 批量打开指定卡片中的成功结果
          const successResults = results.filter(
            r =>
              cardIds.includes(r.id) &&
              r.status === 'success' &&
              r.playgroundUrl,
          );

          console.log(
            `[批量打开选中] 开始打开 ${successResults.length} 个选中的成功结果`,
          );

          let openedCount = 0;
          let failedCount = 0;

          // 改进的批量打开策略 - 使用 Promise 处理异步检测
          const openPromises = successResults.map(async (result, i) => {
            if (result.playgroundUrl) {
              try {
                const newWindow = window.open(
                  result.playgroundUrl,
                  '_blank',
                  'noopener,noreferrer',
                );

                // 使用 Promise 来异步检测窗口状态
                return new Promise<{ success: boolean; url: string }>((resolve) => {
                  setTimeout(() => {
                    if (newWindow === null || (newWindow && newWindow.closed)) {
                      console.warn(
                        `[批量打开选中] 第 ${i + 1} 个链接被浏览器阻止: ${result.playgroundUrl}`,
                      );
                      resolve({ success: false, url: result.playgroundUrl });
                    } else {
                      console.log(
                        `[批量打开选中] 成功打开第 ${i + 1} 个链接: ${result.playgroundUrl}`,
                      );
                      resolve({ success: true, url: result.playgroundUrl });
                    }
                  }, 50);
                });
              } catch (error) {
                console.error(
                  `[批量打开选中] 打开第 ${i + 1} 个链接时出错:`,
                  error,
                );
                return Promise.resolve({ success: false, url: result.playgroundUrl });
              }
            }
            return Promise.resolve({ success: false, url: result.playgroundUrl });
          });

          // 等待所有打开操作完成
          const openResults = await Promise.all(openPromises);
          
          // 计算成功和失败的数量
          openedCount = openResults.filter(r => r.success).length;
          failedCount = openResults.filter(r => !r.success).length;

          // 改进的用户反馈 - 只在有失败时才显示alert
          if (failedCount === 0) {
            // 全部成功，不显示alert
            console.log(`[批量打开选中] 全部成功：${openedCount} 个链接已打开`);
          } else if (openedCount > 0) {
            alert(
              `⚠️ 批量打开选中结果：\n\n` +
                `✅ 成功打开：${openedCount} 个\n` +
                `❌ 失败/被阻止：${failedCount} 个\n\n` +
                `建议：允许浏览器弹窗后重试`,
            );
          } else {
            alert(
              `❌ 所有选中的链接都被浏览器阻止了！\n\n` +
                `解决方法：\n` +
                `1. 点击浏览器地址栏的弹窗阻止图标，选择"允许弹窗"\n` +
                `2. 刷新页面后重试`,
            );
          }

          console.log(
            `[批量打开选中] 完成：成功 ${openedCount} 个，失败 ${failedCount} 个`,
          );
        }
        break;
      case 'export':
        const exportData = results
          .filter(r => cardIds.includes(r.id))
          .map(r => ({
            query: r.query,
            status: r.status,
            playgroundUrl: r.playgroundUrl,
            timestamp: r.timestamp,
          }));
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `batch-results-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        console.log(`导出了 ${exportData.length} 个结果`);
        break;
      case 'delete':
        // 这里可以触发父组件的删除回调
        console.log('删除卡片:', cardIds);
        break;
      case 'clear':
        setSelectedCardIds([]);
        break;
    }
  };

  const handleCardReorder = (fromIndex: number, toIndex: number) => {
    // 这里可以触发父组件的重排序回调
    console.log('重排序:', fromIndex, 'to', toIndex);
  };

  if (results.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center py-16">
          <div
            className="w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg"
            style={{
              background: `linear-gradient(135deg, ${colors.primary[50]} 0%, ${colors.secondary[50]} 100%)`,
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10"
              style={{ color: colors.primary[500] }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-3">
            暂无处理结果
          </h3>
          <p className="text-gray-500 max-w-sm mx-auto leading-relaxed">
            请在左侧输入查询内容并点击"开始处理"按钮来生成处理结果
          </p>
          <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-gray-400">
            <Icon type="tip" color="gray" size="sm" />
            <span>支持批量处理多个查询</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="results-panel-container"
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 0,
        overflow: 'hidden',
      }}
    >
      {/* 优化的头部区域 */}
      <div className="backdrop-blur-sm rounded-xl flex-shrink-0">
        {/* 标题区域 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div
              className="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm"
              style={{
                background: `linear-gradient(135deg, ${colors.primary[100]} 0%, ${colors.secondary[100]} 100%)`,
              }}
            >
              <Icon type="list" color="primary" size="sm" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">
                处理结果列表
              </h3>
              <div className="text-sm text-gray-500">
                共 {results.length} 条，显示 {filteredResults.length} 条
              </div>
              {/* 数据源统计 */}
              {results.length > 0 && (
                <div className="mt-2">
                  <DataSourceStats
                    stats={dataSourceStats}
                    total={results.length}
                    compact={true}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 右侧操作按钮组 */}
          <div className="flex flex-wrap gap-3">
            {/* 视图切换器 - Switch样式 */}
            <div className="view-mode-switcher">
              <button
                onClick={() => setViewMode('card')}
                className={`view-mode-btn ${viewMode === 'card' ? 'active' : ''}`}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <rect x="3" y="3" width="7" height="7" />
                  <rect x="14" y="3" width="7" height="7" />
                  <rect x="14" y="14" width="7" height="7" />
                  <rect x="3" y="14" width="7" height="7" />
                </svg>
                卡片
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`view-mode-btn ${viewMode === 'list' ? 'active' : ''}`}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <line x1="8" y1="6" x2="21" y2="6" />
                  <line x1="8" y1="12" x2="21" y2="12" />
                  <line x1="8" y1="18" x2="21" y2="18" />
                  <line x1="3" y1="6" x2="3.01" y2="6" />
                  <line x1="3" y1="12" x2="3.01" y2="12" />
                  <line x1="3" y1="18" x2="3.01" y2="18" />
                </svg>
                列表
              </button>
            </div>

            <button
              onClick={() => setStatusFilter(null)}
              className={`status-filter-btn px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2 ${
                statusFilter === null
                  ? 'selected all'
                  : 'unselected'
              }`}
            >
              <span>全部</span>
              <span className="count-badge rounded-full px-2 py-0.5 text-xs font-mono">
                {results.length}
              </span>
            </button>
            <button
              onClick={() => setStatusFilter('success')}
              className={`status-filter-btn px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2 ${
                statusFilter === 'success'
                  ? 'selected success'
                  : 'unselected'
              }`}
            >
              <span>成功</span>
              <span className="count-badge rounded-full px-2 py-0.5 text-xs font-mono">
                {stats.success}
              </span>
            </button>
            <button
              onClick={() => setStatusFilter('error')}
              className={`status-filter-btn px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2 ${
                statusFilter === 'error'
                  ? 'selected error'
                  : 'unselected'
              }`}
            >
              <span>失败</span>
              <span className="count-badge rounded-full px-2 py-0.5 text-xs font-mono">
                {stats.error}
              </span>
            </button>
            <button
              onClick={() => setStatusFilter('processing')}
              className={`status-filter-btn px-3 py-1.5 rounded-lg text-sm font-medium flex items-center space-x-2 ${
                statusFilter === 'processing'
                  ? 'selected processing'
                  : 'unselected'
              }`}
            >
              <span>处理中</span>
              <span className="count-badge rounded-full px-2 py-0.5 text-xs font-mono">
                {stats.processing}
              </span>
            </button>
          </div>
        </div>
      </div>

      <div className="flex items-center mb-4 space-x-3 justify-end">
        {/* 打开所有成功结果按钮 - 只在有成功结果且有playgroundUrl时显示 */}
        {results.filter(r => r.status === 'success' && r.playgroundUrl).length >
          0 && (
          <button
            onClick={async () => {
              // 🔍 添加详细的调试信息
              const allSuccessResults = results.filter(
                r => r.status === 'success',
              );
              const successWithUrl = results.filter(
                r => r.status === 'success' && r.playgroundUrl,
              );
              const successWithoutUrl = allSuccessResults.filter(
                r => !r.playgroundUrl,
              );

              console.log(
                `[批量打开调试] 全部成功结果: ${allSuccessResults.length} 个`,
              );
              console.log(
                `[批量打开调试] 有URL的成功结果: ${successWithUrl.length} 个`,
              );
              console.log(
                `[批量打开调试] 没有URL的成功结果: ${successWithoutUrl.length} 个`,
              );

              if (successWithoutUrl.length > 0) {
                console.warn(
                  `[批量打开调试] 没有URL的结果详情:`,
                  successWithoutUrl.map(r => ({
                    id: r.id,
                    query: r.query.substring(0, 50),
                    status: r.status,
                    playgroundUrl: r.playgroundUrl,
                    hasError: !!r.error,
                  })),
                );
              }

              if (successWithUrl.length > 0) {
                console.log(
                  `[批量打开调试] 有URL的结果详情:`,
                  successWithUrl.map(r => ({
                    id: r.id,
                    query: r.query.substring(0, 50),
                    url: r.playgroundUrl,
                  })),
                );
              }

              const successResults = successWithUrl;
              if (successResults.length === 0) {
                alert(
                  `❌ 没有可打开的链接！\n\n调试信息：\n- 成功结果：${allSuccessResults.length} 个\n- 有链接的：${successWithUrl.length} 个\n- 缺少链接的：${successWithoutUrl.length} 个\n\n请检查处理结果是否正确生成了Playground链接。`,
                );
                return;
              }

              console.log(
                `[批量打开] 开始打开 ${successResults.length} 个成功结果的链接`,
              );

              let openedCount = 0;
              let failedCount = 0;
              const failedUrls: string[] = [];

              // 批量打开所有成功结果的链接 - 使用 Promise 处理异步检测
              const openPromises = successResults.map(async (result, i) => {
                if (result.playgroundUrl) {
                  try {
                    // 尝试打开窗口
                    const newWindow = window.open(
                      result.playgroundUrl,
                      '_blank',
                      'noopener,noreferrer',
                    );

                    // 使用 Promise 来异步检测窗口状态
                    return new Promise<{ success: boolean; url: string }>((resolve) => {
                      setTimeout(() => {
                        if (newWindow === null || (newWindow && newWindow.closed)) {
                          // 窗口被阻止或立即关闭
                          console.warn(
                            `[批量打开] 第 ${i + 1} 个链接被浏览器阻止: ${result.playgroundUrl}`,
                          );
                          resolve({ success: false, url: result.playgroundUrl });
                        } else {
                          console.log(
                            `[批量打开] 成功打开第 ${i + 1} 个链接: ${result.playgroundUrl}`,
                          );
                          resolve({ success: true, url: result.playgroundUrl });
                        }
                      }, 50);
                    });
                  } catch (error) {
                    console.error(
                      `[批量打开] 打开第 ${i + 1} 个链接时出错:`,
                      error,
                    );
                    return Promise.resolve({ success: false, url: result.playgroundUrl });
                  }
                }
                return Promise.resolve({ success: false, url: result.playgroundUrl });
              });

              // 等待所有打开操作完成
              const openResults = await Promise.all(openPromises);
              
              // 计算成功和失败的数量
              openedCount = openResults.filter(r => r.success).length;
              failedCount = openResults.filter(r => !r.success).length;
              openResults.filter(r => !r.success).forEach(r => failedUrls.push(r.url));

              // 改进的用户反馈 - 只在有失败时才显示alert
              if (failedCount === 0) {
                // 全部成功，不显示alert
                console.log(`[批量打开] 全部成功：${openedCount} 个链接已打开`);
              } else if (openedCount > 0) {
                // 部分成功
                alert(
                  `⚠️ 批量打开结果：\n\n` +
                    `✅ 成功打开：${openedCount} 个\n` +
                    `❌ 失败/被阻止：${failedCount} 个\n\n` +
                    `建议：\n` +
                    `1. 允许浏览器弹窗（点击地址栏的弹窗图标）\n` +
                    `2. 或者在卡片视图中逐个点击打开`,
                );
              } else {
                // 全部失败
                alert(
                  `❌ 所有链接都被浏览器阻止了！\n\n` +
                    `解决方法：\n` +
                    `1. 点击浏览器地址栏的弹窗阻止图标，选择"允许弹窗"\n` +
                    `2. 刷新页面后重试\n` +
                    `3. 或者在卡片视图中逐个点击打开链接`,
                );
              }

              console.log(
                `[批量打开] 完成：成功 ${openedCount} 个，失败 ${failedCount} 个`,
              );
            }}
            className="btn btn-sm btn--secondary-glass flex items-center space-x-2 open-all-results-btn relative"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
            <span>打开所有成功结果</span>
            <span className="bg-white/30 rounded-full px-2 py-0.5 text-xs font-mono glow-text ">
              {
                results.filter(r => r.status === 'success' && r.playgroundUrl)
                  .length
              }
            </span>
            {/* 星光emoji - hover时出现并旋转闪亮 */}
            <span className="starlight-emoji absolute -top-1 -right-1 text-lg opacity-0 transition-all duration-300">
              ✨
            </span>
          </button>
        )}

        {/* 复制成功结果列表按钮 */}
        {stats.success > 0 && (
          <button
            onClick={async () => {
              const successResults = results.filter(
                r => r.status === 'success',
              );
              if (successResults.length === 0) {
                return;
              }

              // 创建格式化的列表
              const listContent = successResults
                .map(r => `${r.query}: ${r.playgroundUrl || '无链接'}`)
                .join('\n');

              try {
                await navigator.clipboard.writeText(listContent);
                setCopySuccess(true);
                setTimeout(() => setCopySuccess(false), 2000);
                console.log('成功结果列表已复制到剪贴板');
              } catch (error) {
                console.error('复制失败:', error);
              }
            }}
            className="btn btn-sm flex items-center space-x-2 btn--secondary-glass"
          >
            {copySuccess ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            )}
            <span>{copySuccess ? '已复制！' : '复制成功结果列表'}</span>
            <span className="bg-white/30 rounded-full px-2 py-0.5 text-xs font-mono">
              {stats.success}
            </span>
          </button>
        )}

        {/* 重试失败项按钮 */}
        {!isRunning && progress && progress.failed > 0 && onRetryFailed && (
          <button
            onClick={async () => {
              try {
                await onRetryFailed();
              } catch (error) {
                console.error('重试失败:', error);
              }
            }}
            className={`btn btn-sm btn--primary-gold flex items-center space-x-2 retry-failed-btn ${isRetrying ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={isRetrying}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            <span>{isRetrying ? '重试中...' : '重试失败项'}</span>
            <span className="retry-failed-badge rounded-full px-2 py-0.5 text-xs font-mono font-semibold">
              {progress.failed}
            </span>
          </button>
        )}
      </div>
      {/* 内容区域 - 根据视图模式显示不同内容 */}
      <div
        className="results-content-container"
        style={{
          flex: 1,
          minHeight: 0,
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        {viewMode === 'card' ? (
          // 卡片视图
          <ResultCardsGrid
            results={filteredResults}
            focusedCardId={focusedCardId || undefined}
            selectedCardIds={selectedCardIds}
            onCardFocus={handleCardFocus}
            onCardSelection={handleCardSelection}
            onBatchAction={handleBatchAction}
            onCardReorder={handleCardReorder}
          />
        ) : (
          // 列表视图
          <div
            className="results-list-container compact-list-container"
            style={{
              paddingRight: '8px',
              marginRight: '-8px',
            }}
          >
            <div
              className="results-list-content compact"
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
                paddingBottom: '12px',
              }}
            >
              {filteredResults.map(result => {
                const { id, query, status, playgroundUrl, error, processTime } =
                  result;

                // 根据状态选择图标和颜色 - 使用主题色
                let icon: React.ReactNode, statusText: string;
                let iconStyle: React.CSSProperties,
                  cardStyle: React.CSSProperties;

                let hoverBorderColor: string;

                if (status === 'success') {
                  icon = <Icon type="check" color="success" size="sm" />;
                  iconStyle = {
                    background: colors.success.highlight,
                    color: colors.success.dark,
                    border: 'none',
                    boxShadow: '0 2px 8px rgba(100, 181, 246, 0.25)',
                  };
                  cardStyle = {
                    background: colors.success.highlight,
                    borderColor: `${colors.success.main}40`,
                    boxShadow: '0 2px 8px rgba(100, 181, 246, 0.15)',
                  };
                  hoverBorderColor = colors.primary[400]; // 蓝色边框
                  statusText = '处理成功';
                } else if (status === 'error') {
                  icon = <Icon type="error" color="error" size="sm" />;
                  iconStyle = {
                    background: colors.error.disabled,
                    color: colors.gray[500],
                    border: 'none',
                    boxShadow: '0 1px 3px rgba(148, 163, 184, 0.15)',
                    opacity: 0.8,
                  };
                  cardStyle = {
                    background: colors.error.disabled,
                    borderColor: `${colors.gray[300]}60`,
                    boxShadow: '0 1px 3px rgba(148, 163, 184, 0.08)',
                    opacity: 0.9,
                  };
                  hoverBorderColor = '#F0E68C'; // 浅黄色边框
                  statusText = '处理失败';
                } else if (status === 'processing') {
                  icon = (
                    <Icon
                      type="processing"
                      color="processing"
                      size="sm"
                      animate
                    />
                  );
                  iconStyle = {
                    background: `linear-gradient(135deg, ${colors.warning.light} 0%, rgba(255, 255, 255, 0.9) 100%)`,
                    color: colors.warning.dark,
                    border: 'none',
                    boxShadow: '0 2px 8px rgba(255, 213, 79, 0.25)',
                  };
                  cardStyle = {
                    background: `linear-gradient(135deg, ${colors.warning.light} 0%, rgba(255, 255, 255, 0.95) 100%)`,
                    borderColor: `${colors.warning.main}40`,
                    boxShadow: '0 2px 8px rgba(255, 213, 79, 0.15)',
                  };
                  hoverBorderColor = colors.warning.main; // 黄色边框
                  statusText = '处理中...';
                } else {
                  icon = <Icon type="clock" color="neutral" size="sm" />;
                  iconStyle = {
                    background: `linear-gradient(135deg, ${colors.gray[50]} 0%, rgba(255, 255, 255, 0.9) 100%)`,
                    color: colors.gray[500],
                    border: 'none',
                    boxShadow: '0 2px 8px rgba(148, 163, 184, 0.15)',
                  };
                  cardStyle = {
                    background: `linear-gradient(135deg, ${colors.gray[50]} 0%, rgba(255, 255, 255, 0.95) 100%)`,
                    borderColor: `${colors.gray[200]}60`,
                    boxShadow: '0 2px 8px rgba(148, 163, 184, 0.10)',
                  };
                  hoverBorderColor = colors.gray[400]; // 灰色边框
                  statusText = '等待处理';
                }

                return (
                  <div
                    key={id}
                    className="rounded-lg shadow-sm p-3 transition-all duration-300 hover:shadow-lg compact-list-item"
                    style={{
                      ...cardStyle,
                      margin: '4px 0',
                      border: 'none', // 简化：移除边框，使用阴影分离
                      borderColor: cardStyle.borderColor,
                    }}
                    onMouseEnter={e => {
                      e.currentTarget.style.borderColor = hoverBorderColor;
                      e.currentTarget.style.transform = 'translateY(-1px)';
                      if (status === 'success') {
                        e.currentTarget.style.boxShadow =
                          '0 4px 16px rgba(100, 181, 246, 0.25)';
                      } else if (status === 'error') {
                        e.currentTarget.style.boxShadow =
                          '0 2px 8px rgba(240, 230, 140, 0.35)';
                      } else if (status === 'processing') {
                        e.currentTarget.style.boxShadow =
                          '0 4px 16px rgba(255, 213, 79, 0.25)';
                      } else {
                        e.currentTarget.style.boxShadow =
                          '0 4px 16px rgba(148, 163, 184, 0.20)';
                      }
                    }}
                    onMouseLeave={e => {
                      e.currentTarget.style.borderColor =
                        cardStyle.borderColor as string;
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow =
                        cardStyle.boxShadow as string;
                    }}
                  >
                    <div className="space-y-2">
                      {/* 主要内容行：状态图标、查询内容、操作按钮 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          {/* 状态图标 */}
                          <Tooltip content={statusText} position="top">
                            <div
                              className="flex-shrink-0 p-1.5 rounded-md shadow-sm"
                              style={iconStyle}
                            >
                              {icon}
                            </div>
                          </Tooltip>

                          {/* 查询内容 */}
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate compact-title">
                              {query}
                            </h4>
                          </div>

                          {/* 处理时间 */}
                          {processTime && (
                            <span
                              className="text-xs px-1.5 py-0.5 rounded font-mono flex-shrink-0"
                              style={{
                                backgroundColor: colors.gray[100],
                                color: colors.gray[600],
                              }}
                            >
                              {(processTime / 1000).toFixed(2)}s
                            </span>
                          )}

                          {/* 数据源标签 */}
                          {result.dataSource && (
                            <DataSourceLabel
                              dataSource={result.dataSource}
                              internalDataSummary={result.internalDataSummary}
                              compact={true}
                              showDetails={true}
                            />
                          )}
                        </div>

                        {/* 右侧操作区域 */}
                        <div className="flex items-center px-3 space-x-1.5 flex-shrink-0">
                          {/* 成功状态的高光预览按钮 */}
                          {status === 'success' && playgroundUrl && (
                            <Tooltip
                              content="点击打开Playground预览链接"
                              position="left"
                            >
                              <a
                                href={playgroundUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center space-x-1.5 px-3 py-1.5 rounded-md transition-all duration-300 text-xs font-medium transform hover:scale-105 animate-buttonFloat"
                                style={{
                                  background: colors.success.gradient,
                                  color: colors.success.dark,
                                  border: 'none', // 简化：移除边框，依靠背景色区分
                                  boxShadow:
                                    '0 2px 8px rgba(100, 181, 246, 0.2)', // 简化：减少阴影强度
                                  backdropFilter: 'blur(8px)',
                                }}
                                onMouseEnter={e => {
                                  e.currentTarget.style.background = `linear-gradient(135deg, ${colors.primary[100]} 0%, ${colors.primary[200]} 50%, ${colors.primary[300]} 100%)`;
                                  e.currentTarget.style.boxShadow =
                                    '0 6px 20px rgba(100, 181, 246, 0.35)';
                                  e.currentTarget.style.transform =
                                    'scale(1.05) translateY(-2px)';
                                }}
                                onMouseLeave={e => {
                                  e.currentTarget.style.background =
                                    colors.success.gradient;
                                  e.currentTarget.style.boxShadow =
                                    '0 4px 12px rgba(100, 181, 246, 0.25)';
                                  e.currentTarget.style.transform = 'scale(1)';
                                }}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                  />
                                </svg>
                                <span>打开预览</span>
                                <div className="w-2 h-2 bg-white/30 rounded-full animate-pulse" />
                              </a>
                            </Tooltip>
                          )}

                          {/* Lynx转Web预览功能已整合到卡片视图的InteractiveIframe中 */}

                          {/* 错误状态的错误按钮 */}
                          {status === 'error' && error && (
                            <Tooltip content={error} position="left">
                              <div
                                className="p-1.5 rounded-md cursor-not-allowed"
                                style={{
                                  background: colors.error.disabled,
                                  color: colors.gray[500],
                                  border: 'none', // 简化：移除边框
                                  boxShadow:
                                    '0 1px 3px rgba(148, 163, 184, 0.1)', // 简化：减少阴影
                                  opacity: 0.8,
                                }}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                              </div>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* 优化的空状态显示 */}
      {statusFilter && filteredResults.length === 0 && (
        <div
          className="results-list-container"
          style={{
            flex: 1,
            minHeight: 0,
            overflowY: 'auto',
            overflowX: 'hidden',
          }}
        >
          <div className="results-filter-empty">
            <div className="results-filter-empty-content">
              <div className="results-filter-empty-icon results-filter-empty-icon-gradient">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="results-filter-empty-icon-svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.881-6.08 2.33"
                  />
                </svg>
              </div>
              <h3 className="results-filter-empty-title">没有找到匹配的结果</h3>
              <p className="results-filter-empty-description">
                没有匹配"
                {statusFilter === 'success'
                  ? '成功'
                  : statusFilter === 'error'
                    ? '失败'
                    : statusFilter === 'processing'
                      ? '处理中'
                      : '待处理'}
                "状态的结果
              </p>
              <button
                onClick={() => setStatusFilter(null)}
                className="results-filter-empty-btn"
              >
                查看所有结果
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsPanel;
