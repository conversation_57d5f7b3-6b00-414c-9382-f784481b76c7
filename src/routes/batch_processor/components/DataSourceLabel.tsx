/**
 * 数据源标签组件
 * 
 * 🎯 核心功能：
 * - 显示结果的数据源类型（底稿数据/AI生成/回退模式）
 * - 提供清晰的视觉标识和颜色区分
 * - 支持悬停显示详细信息
 * - 响应式设计，适配不同屏幕尺寸
 * 
 * 📊 数据源类型：
 * - internal: 使用抖音内部底稿数据生成
 * - ai: AI直接生成
 * - fallback: 底稿数据获取失败后回退到AI生成
 * 
 * 🎨 设计原则：
 * - 颜色编码：绿色(底稿数据)、蓝色(AI生成)、橙色(回退模式)
 * - 图标标识：不同数据源使用不同的图标
 * - 信息透明：用户可以清楚知道内容的来源
 * 
 * <AUTHOR>
 * @since 2025-07-28
 * @version 1.0.0
 */

import React from 'react';
import { Tooltip } from '@douyinfe/semi-ui';
import Icon from './Icon';

/**
 * 数据源标签属性接口
 */
export interface DataSourceLabelProps {
  /** 数据源类型 */
  dataSource: 'internal' | 'ai' | 'fallback';
  /** 底稿数据摘要（仅当dataSource为internal时显示） */
  internalDataSummary?: string;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 紧凑模式（更小的尺寸） */
  compact?: boolean;
}

/**
 * 数据源配置映射
 */
const DATA_SOURCE_CONFIG = {
  internal: {
    label: '底稿数据',
    color: '#52c41a', // 绿色
    bgColor: '#f6ffed',
    borderColor: '#b7eb8f',
    icon: 'database',
    description: '基于抖音内部底稿数据生成，确保信息准确性和权威性',
  },
  ai: {
    label: 'AI生成',
    color: '#1890ff', // 蓝色
    bgColor: '#f0f9ff',
    borderColor: '#91d5ff',
    icon: 'robot',
    description: 'AI直接生成内容，响应快速但可能缺乏特定数据',
  },
  fallback: {
    label: '回退模式',
    color: '#fa8c16', // 橙色
    bgColor: '#fff7e6',
    borderColor: '#ffd591',
    icon: 'refresh',
    description: '底稿数据获取失败，已自动回退到AI直接生成模式',
  },
} as const;

/**
 * 数据源标签组件
 */
export const DataSourceLabel: React.FC<DataSourceLabelProps> = ({
  dataSource,
  internalDataSummary,
  showDetails = true,
  className = '',
  compact = false,
}) => {
  const config = DATA_SOURCE_CONFIG[dataSource];
  
  // 构建详细信息内容
  const getTooltipContent = () => {
    let content = config.description;
    
    if (dataSource === 'internal' && internalDataSummary) {
      content += `\n\n📊 数据摘要：${internalDataSummary}`;
    }
    
    if (dataSource === 'fallback') {
      content += '\n\n💡 提示：您可以稍后重试以尝试获取底稿数据';
    }
    
    return content;
  };

  // 基础标签样式
  const labelStyle: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: compact ? '4px' : '6px',
    padding: compact ? '2px 6px' : '4px 8px',
    fontSize: compact ? '11px' : '12px',
    fontWeight: 500,
    color: config.color,
    backgroundColor: config.bgColor,
    border: `1px solid ${config.borderColor}`,
    borderRadius: '4px',
    cursor: showDetails ? 'help' : 'default',
    transition: 'all 0.2s ease',
    whiteSpace: 'nowrap' as const,
  };

  // 悬停效果样式
  const hoverStyle: React.CSSProperties = {
    ...labelStyle,
    backgroundColor: config.color,
    color: '#fff',
    transform: 'translateY(-1px)',
    boxShadow: `0 2px 8px ${config.color}20`,
  };

  const [isHovered, setIsHovered] = React.useState(false);

  const labelElement = (
    <span
      className={`data-source-label ${className}`}
      style={isHovered ? hoverStyle : labelStyle}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Icon 
        name={config.icon} 
        size={compact ? 12 : 14}
        style={{ color: 'currentColor' }}
      />
      <span>{config.label}</span>
      
      {/* 底稿数据特殊标识 */}
      {dataSource === 'internal' && (
        <span style={{ 
          fontSize: compact ? '10px' : '11px',
          opacity: 0.8,
          marginLeft: '2px'
        }}>
          ✓
        </span>
      )}
      
      {/* 回退模式警告标识 */}
      {dataSource === 'fallback' && (
        <span style={{ 
          fontSize: compact ? '10px' : '11px',
          opacity: 0.8,
          marginLeft: '2px'
        }}>
          ⚠
        </span>
      )}
    </span>
  );

  // 如果不显示详细信息，直接返回标签
  if (!showDetails) {
    return labelElement;
  }

  // 使用Tooltip包装显示详细信息
  return (
    <Tooltip
      content={
        <div style={{ 
          maxWidth: '300px', 
          whiteSpace: 'pre-line',
          lineHeight: '1.4'
        }}>
          {getTooltipContent()}
        </div>
      }
      position="top"
      trigger="hover"
      showArrow
    >
      {labelElement}
    </Tooltip>
  );
};

/**
 * 数据源标签列表组件 - 用于显示多个数据源统计
 */
export interface DataSourceStatsProps {
  /** 数据源统计 */
  stats: {
    internal: number;
    ai: number;
    fallback: number;
  };
  /** 总数 */
  total: number;
  /** 紧凑模式 */
  compact?: boolean;
}

export const DataSourceStats: React.FC<DataSourceStatsProps> = ({
  stats,
  total,
  compact = false,
}) => {
  if (total === 0) return null;

  return (
    <div style={{ 
      display: 'flex', 
      gap: compact ? '8px' : '12px',
      alignItems: 'center',
      flexWrap: 'wrap' as const,
    }}>
      {Object.entries(stats).map(([source, count]) => {
        if (count === 0) return null;
        
        const percentage = Math.round((count / total) * 100);
        const config = DATA_SOURCE_CONFIG[source as keyof typeof DATA_SOURCE_CONFIG];
        
        return (
          <div
            key={source}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              fontSize: compact ? '11px' : '12px',
              color: config.color,
            }}
          >
            <Icon name={config.icon} size={compact ? 12 : 14} />
            <span>{config.label}</span>
            <span style={{ 
              fontWeight: 'bold',
              marginLeft: '2px'
            }}>
              {count}
            </span>
            <span style={{ 
              opacity: 0.7,
              fontSize: compact ? '10px' : '11px'
            }}>
              ({percentage}%)
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default DataSourceLabel;
