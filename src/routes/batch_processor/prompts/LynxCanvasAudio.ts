/**
 * Lynx Canvas Audio API Guide for Claude4
 * 结构化优化，提高 AI 理解和应用效率
 */

export const LYNX_CANVAS_AUDIO_PROMPT = [
  '',
  '═══════════════════════════════════════════════════════════════',
  '🎯 LYNX CANVAS AUDIO MASTER GUIDE FOR CLAUDE4',
  '═══════════════════════════════════════════════════════════════',
  '',
  'CONTEXT: Lynx Canvas Audio API with Web Audio Context',
  'GOAL: Generate working audio visualization and playback code',
  'CRITICAL: Audio requires proper lifecycle management and Canvas integration',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🚨 TOP 5 CRITICAL SUCCESS FACTORS (MUST FOLLOW)            │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '1️⃣ AUDIO CONTEXT INITIALIZATION MANDATORY',
  '   ❌ Missing audioContext = Silent failure',
  '   ✅ this.audioContext = lynx.getAudioContext();',
  '   ✅ this.analyser = this.audioContext.createAnalyser();',
  '',
  '2️⃣ CANVAS INTEGRATION REQUIREMENTS',
  '   ❌ Missing canvas setup',
  '   ✅ this.canvas = lynx.createCanvas("canvasId");',
  '   ✅ Canvas touch events for interaction',
  '',
  '3️⃣ AUDIO NODE CONNECTION PATTERN',
  '   ❌ Direct audio.play()',
  '   ✅ node.connect(audioContext.destination)',
  '   ✅ node.connect(analyser) for visualization',
  '',
  '4️⃣ LIFECYCLE MANAGEMENT CRITICAL',
  '   ❌ Memory leaks from unmanaged audio',
  '   ✅ onViewDisappeared() pause audio',
  '   ✅ onViewAppeared() resume audio',
  '   ✅ Proper node disconnection',
  '',
  '5️⃣ VISUALIZATION FRAME LOOP',
  '   ❌ Static audio without visual feedback',
  '   ✅ lynx.requestAnimationFrame(drawFrame)',
  '   ✅ analyser.getByteTimeDomainData()',
  '   ✅ analyser.getByteFrequencyData()',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🎵 AUDIO SOURCE TYPE PATTERNS                              │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '🎧 AUDIO FILE PLAYBACK PATTERN',
  'playAudioSrc(src) {',
  '  const audio = lynx.createAudio(src);',
  '  const source = this.audioContext.createMediaElementSource(audio);',
  '  audio.loop = true;',
  '  audio.autoplay = true;',
  '  return source;  // Connect to destination + analyser',
  '}',
  'SUPPORTED: WAV, MP3, MP4, M4A, AAC (OGG not in Lynx)',
  '',
  '🎛️ OSCILLATOR GENERATION PATTERN',
  'playOscillator(type) {',
  '  const oscillator = this.audioContext.createOscillator();',
  '  oscillator.type = type;  // "sine", "square", "sawtooth", "triangle"',
  '  oscillator.start();',
  '  return oscillator;',
  '}',
  '',
  '🎤 MICROPHONE INPUT PATTERN',
  'onTapMic() {',
  '  lynx.getUserMedia({audio: true}, (stream) => {',
  '    const node = this.audioContext.createMediaStreamSource(stream);',
  '    this.switchAudioNode(node);',
  '  }, err => console.error("Mic failed:", err));',
  '}',
  '',
  '🔊 SINE WAVE GENERATION PATTERN',
  'createSinBuffer(ctx, freq) {',
  '  const bufferLength = Math.ceil(ctx.sampleRate * 3 / freq) * freq;',
  '  const buf = ctx.createBuffer(2, bufferLength, ctx.sampleRate);',
  '  // Fill buffer with sine wave data',
  '  return buf;',
  '}',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 📊 AUDIO VISUALIZATION PATTERNS                           │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '🌊 WAVEFORM VISUALIZATION',
  'startAnalyse(canvas, analyser) {',
  '  const bufferLength = analyser.frequencyBinCount;',
  '  const timeArray = new Uint8Array(bufferLength);',
  '  const freqArray = new Uint8Array(bufferLength);',
  '  ',
  '  return () => {',
  '    analyser.getByteTimeDomainData(timeArray);',
  '    analyser.getByteFrequencyData(freqArray);',
  '    // Draw waveform and spectrum',
  '  };',
  '}',
  '',
  '📈 FREQUENCY SPECTRUM VISUALIZATION',
  '// Blue waveform + Green frequency spectrum',
  'context.strokeStyle = "blue";   // Waveform',
  'context.strokeStyle = "green";  // Frequency',
  'const sliceWidth = canvas.width / bufferLength;',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🔧 AUDIO NODE MANAGEMENT PATTERN                          │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '🔄 AUDIO NODE SWITCHING (CRITICAL)',
  'switchAudioNode(node, bufferNode = null) {',
  '  // Disconnect old node',
  '  if (this.audioNode) {',
  '    this.audioNode.disconnect(this.audioContext.destination);',
  '    this.audioNode.disconnect(this.analyser);',
  '  }',
  '  ',
  '  // Stop old audio',
  '  if (this.oldAudio) {',
  '    this.oldAudio.stop();',
  '    this.oldAudio = null;',
  '  }',
  '  ',
  '  // Connect new node',
  '  this.audioNode = node;',
  '  if (node) {',
  '    node.connect(this.audioContext.destination);',
  '    node.connect(this.analyser);',
  '  }',
  '}',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 📁 COMPLETE AUDIO CANVAS TEMPLATE                         │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '// index.ttml',
  '<scroll-view style="height: 100vh; max-height: 100vh;" scroll-y="true">',
  '  <view class="audio-container">',
  '    <canvas style="width: 100%; height: 50%;" name="audioCanvas"></canvas>',
  '    <view class="controls">',
  '      <view bindtap="onTapSin" class="btn">正弦波</view>',
  '      <view bindtap="onTapMic" class="btn">麦克风</view>',
  '      <view bindtap="onTapWav" class="btn">播放WAV</view>',
  '    </view>',
  '  </view>',
  '</scroll-view>',
  '',
  '// index.js',
  'Card({',
  '  data: {',
  '    testCase: "",',
  '    title: "",',
  '    engineType: "default"',
  '  },',
  '  ',
  '  canvas: null,',
  '  audioContext: null,',
  '  audioNode: null,',
  '  analyser: null,',
  '  frameFunction: null,',
  '  ',
  '  onReady() {',
  '    // Initialize canvas',
  '    this.canvas = lynx.createCanvas("audioCanvas");',
  '    ',
  '    // Initialize audio context',
  '    this.audioContext = lynx.getAudioContext();',
  '    this.analyser = this.audioContext.createAnalyser();',
  '    this.analyser.fftSize = 512;',
  '    ',
  '    // Start animation loop',
  '    this.startAnimationLoop();',
  '  },',
  '  ',
  '  startAnimationLoop() {',
  '    const drawFrame = () => {',
  '      lynx.requestAnimationFrame(drawFrame);',
  '      if (this.frameFunction) this.frameFunction();',
  '    };',
  '    lynx.requestAnimationFrame(drawFrame);',
  '  }',
  '});',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ ⚡ PERFORMANCE & LIFECYCLE MANAGEMENT                     │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  'AUDIO ENGINE: lynx.krypton.aurum().engineType',
  'FFT SIZE: analyser.fftSize = 512 (faster than 2048)',
  'FRAME RATE: lynx.requestAnimationFrame for 60fps',
  'MEMORY: Always disconnect nodes and stop audio sources',
  '',
  'VIEW LIFECYCLE:',
  'onViewDisappeared() → lynx.aurum().pause()',
  'onViewAppeared() → lynx.aurum().resume()',
  'onUnload() → Cleanup all audio resources',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🚨 CRITICAL ERROR PATTERNS TO AVOID                       │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '❌ AUDIO CONTEXT MISSING (90% of audio failures)',
  'SYMPTOM: No audio playback or visualization',
  'CAUSE: Missing lynx.getAudioContext() initialization',
  'FIX: Always initialize audioContext in onReady()',
  '',
  '❌ NODE CONNECTION MISSING (80% of audio failures)',
  'SYMPTOM: Audio created but not heard',
  'CAUSE: Missing connect() to destination',
  'FIX: node.connect(audioContext.destination)',
  '',
  '❌ CANVAS NOT INITIALIZED (70% of visualization failures)',
  'SYMPTOM: Audio works but no visualization',
  'CAUSE: Missing lynx.createCanvas() setup',
  'FIX: Initialize canvas with correct name in onReady()',
  '',
  '❌ MEMORY LEAKS (60% of performance issues)',
  'SYMPTOM: App slows down or crashes over time',
  'CAUSE: Not disconnecting old audio nodes',
  'FIX: Always disconnect() before switching nodes',
  '',
  '❌ LIFECYCLE NOT MANAGED (50% of background issues)',
  'SYMPTOM: Audio continues when app backgrounded',
  'CAUSE: Missing view lifecycle handlers',
  'FIX: Implement onViewDisappeared/onViewAppeared',
  '',
  '',
  '═══════════════════════════════════════════════════════════════',
  '🎯 LYNX AUDIO SUCCESS GUARANTEE CHECKLIST',
  '═══════════════════════════════════════════════════════════════',
  '',
  '🔸 MANDATORY INITIALIZATIONS',
  '✅ audioContext = lynx.getAudioContext() in onReady()',
  '✅ canvas = lynx.createCanvas("name") with correct name',
  '✅ analyser = audioContext.createAnalyser() for visualization',
  '✅ analyser.fftSize = 512 for performance',
  '',
  '🔸 AUDIO NODE MANAGEMENT',
  '✅ All audio sources connect to audioContext.destination',
  '✅ All audio sources connect to analyser for visualization',
  '✅ Proper switchAudioNode() implementation',
  '✅ disconnect() old nodes before connecting new ones',
  '',
  '🔸 VISUALIZATION REQUIREMENTS',
  '✅ lynx.requestAnimationFrame() animation loop',
  '✅ getByteTimeDomainData() for waveform',
  '✅ getByteFrequencyData() for spectrum',
  '✅ Canvas 2D context drawing operations',
  '',
  '🔸 LIFECYCLE MANAGEMENT',
  '✅ onViewDisappeared() → pause audio',
  '✅ onViewAppeared() → resume audio',
  '✅ onUnload() → cleanup all resources',
  '✅ Touch events for user interaction',
  '',
  'FINAL VERIFICATION: Does audio play AND visualize correctly?',
  'If YES → Code will work | If NO → Check node connections',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🔧 ADVANCED AUDIO PROCESSING PATTERNS                     │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '🎚️ GAIN CONTROL WITH FADE EFFECTS',
  'const gainNode = audioContext.createGain();',
  'gainNode.gain.value = 0;  // Start silent',
  'source.connect(gainNode);',
  'gainNode.connect(audioContext.destination);',
  '',
  '// Fade in effect',
  'let x = 0;',
  'const fadeIn = lynx.setInterval(() => {',
  '  if (x > 1) {',
  '    gainNode.gain.value = 1;',
  '    clearInterval(fadeIn);',
  '  } else {',
  '    gainNode.gain.value = Math.pow(2, x * 10) / 1024;',
  '    x += 0.05;',
  '  }',
  '}, 100);',
  '',
  '🔄 PLAYBACK RATE CONTROL',
  'updatePlaybackRate(value) {',
  '  const playbackRate = this.audioBufferNode?.playbackRate;',
  '  if (playbackRate) {',
  '    playbackRate.value += value;',
  '    this.setData({',
  '      playbackRate: "调速 " + playbackRate.value.toFixed(2)',
  '    });',
  '  }',
  '}',
  '',
  '🎭 CHANNEL SPLITTING AND MERGING',
  'channelSplitAndMerge() {',
  '  const merger = audioContext.createChannelMerger(2);',
  '  const splitter = audioContext.createChannelSplitter(2);',
  '  ',
  '  // Connect sources to specific channels',
  '  sinWaveNode.connect(merger, 0, 0);  // Left channel',
  '  musicNode.connect(merger, 0, 1);    // Right channel',
  '  ',
  '  // Split for further processing',
  '  merger.connect(splitter);',
  '  return merger;',
  '}',
  '',
  '🎵 AUDIO BUFFER DECODING',
  'decodeAudioData(src) {',
  '  lynx.krypton.readFile(src, (audioData) => {',
  '    if (!audioData) {',
  '      console.error("读取音频文件失败");',
  '      return;',
  '    }',
  '    ',
  '    this.audioContext.decodeAudioData(audioData)',
  '      .then(audioBuffer => {',
  '        const source = this.audioContext.createBufferSource();',
  '        source.buffer = audioBuffer;',
  '        source.loop = false;',
  '        source.start();',
  '        this.switchAudioNode(source, source);',
  '      })',
  '      .catch(err => console.error("解码失败:", err));',
  '  });',
  '}',
  '',
  '🔧 SCRIPT PROCESSOR (Advanced)',
  'scriptProcess(src) {',
  '  const source = audioContext.createScriptProcessor(16384, 0, 2);',
  '  source.onaudioprocess = audioProcessingEvent => {',
  '    const buf = audioProcessingEvent.outputBuffer;',
  '    // Process audio buffer data',
  '    for (let channel = 0; channel < buf.numberOfChannels; channel++) {',
  '      const arr = buf.getChannelData(channel);',
  '      // Custom audio processing logic',
  '    }',
  '  };',
  '  return source;',
  '}',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🎨 CANVAS VISUALIZATION TECHNIQUES                        │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  '🌊 WAVEFORM DRAWING IMPLEMENTATION',
  'drawWaveform(canvas, timeArray) {',
  '  const context = canvas.getContext("2d");',
  '  context.strokeStyle = "blue";',
  '  context.lineWidth = 1;',
  '  context.beginPath();',
  '  ',
  '  const sliceWidth = canvas.width / timeArray.length;',
  '  let x = 0;',
  '  ',
  '  for (let i = 0; i < timeArray.length; i++) {',
  '    const v = timeArray[i] / 128.0;',
  '    const y = v * canvas.height / 2;',
  '    ',
  '    if (i === 0) {',
  '      context.moveTo(x, y);',
  '    } else {',
  '      context.lineTo(x, y);',
  '    }',
  '    x += sliceWidth;',
  '  }',
  '  context.stroke();',
  '}',
  '',
  '📊 FREQUENCY SPECTRUM DRAWING',
  'drawSpectrum(canvas, freqArray) {',
  '  const context = canvas.getContext("2d");',
  '  context.strokeStyle = "green";',
  '  context.lineWidth = 3;',
  '  context.beginPath();',
  '  ',
  '  const sliceWidth = canvas.width / freqArray.length;',
  '  let x = 0;',
  '  ',
  '  for (let i = 0; i < freqArray.length; i++) {',
  '    const v = 2 - freqArray[i] / 128.0;',
  '    const y = v * canvas.height / 2;',
  '    ',
  '    if (i === 0) {',
  '      context.moveTo(x, y);',
  '    } else {',
  '      context.lineTo(x, y);',
  '    }',
  '    x += sliceWidth;',
  '  }',
  '  context.stroke();',
  '}',
  '',
  '🎯 INTERACTIVE CANVAS TOUCH HANDLING',
  'setupCanvasInteraction() {',
  '  this.canvas.addEventListener("touchstart", (event) => {',
  '    if (event.changedTouches) {',
  '      const touch = event.changedTouches[0];',
  '      const valX = touch.clientX * 2 * SystemInfo.pixelRatio > this.canvas.width;',
  '      const valY = touch.clientY * 2 * SystemInfo.pixelRatio > this.canvas.height;',
  '      this.onTouchCanvas(valX, valY);',
  '    }',
  '  });',
  '}',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🚨 DEBUGGING WORKFLOW FOR AUDIO ISSUES                    │',
  '└─────────────────────────────────────────────────────────────┘',
  '',
  'STEP 1: NO AUDIO PLAYBACK',
  '→ Check audioContext initialization',
  '→ Verify node.connect(audioContext.destination)',
  '→ Confirm audio source creation',
  '→ Test with simple oscillator first',
  '',
  'STEP 2: NO VISUALIZATION',
  '→ Check canvas initialization with correct name',
  '→ Verify analyser.connect() setup',
  '→ Confirm requestAnimationFrame loop',
  '→ Test getByteTimeDomainData() data',
  '',
  'STEP 3: AUDIO CUTS OUT',
  '→ Check view lifecycle handlers',
  '→ Verify node disconnection logic',
  '→ Confirm memory management',
  '→ Test background/foreground behavior',
  '',
  'STEP 4: PERFORMANCE ISSUES',
  '→ Reduce analyser.fftSize (512 vs 2048)',
  '→ Optimize canvas drawing operations',
  '→ Limit frame rate if needed',
  '→ Profile memory usage',
  '',
  'STEP 5: INTERACTION PROBLEMS',
  '→ Check touch event setup',
  '→ Verify coordinate calculations',
  '→ Test button bindings',
  '→ Confirm data updates',
  '',
  '',
  '┌─────────────────────────────────────────────────────────────┐',
  '│ 🔥 CRITICAL LYNX AUDIO API RULES                          │',
  '└─────────────────────────────────────────────────────────────┐',
  '',
  '🚨 AUDIO CONTEXT MANAGEMENT',
  'RULE: 必须使用 lynx.getAudioContext() 获取音频上下文',
  'RULE: analyser.fftSize = 512 优化性能',
  'RULE: 音频引擎类型检测 lynx.krypton.aurum().engineType',
  '',
  '🚨 CANVAS INTEGRATION',
  'RULE: 必须使用 lynx.createCanvas("name") 创建画布',
  'RULE: 画布名称必须与 TTML 中 name 属性一致',
  'RULE: 触摸事件需要坐标转换 clientX * 2 * SystemInfo.pixelRatio',
  '',
  '🚨 AUDIO NODE LIFECYCLE',
  'RULE: 所有音频节点必须连接到 destination 和 analyser',
  'RULE: 切换节点前必须 disconnect() 旧节点',
  'RULE: 停止音频源必须调用 stop() 方法',
  '',
  '🚨 VIEW LIFECYCLE INTEGRATION',
  'RULE: onViewDisappeared 必须暂停音频 lynx.aurum().pause()',
  'RULE: onViewAppeared 必须恢复音频 lynx.aurum().resume()',
  'RULE: 全局事件监听器注册 GlobalEventEmitter',
  '',
  '🚨 VISUALIZATION FRAME LOOP',
  'RULE: 必须使用 lynx.requestAnimationFrame() 而不是 setInterval',
  'RULE: 帧函数存储在实例属性 this.frameFunction',
  'RULE: FPS 计算和性能监控是可选的',
  '',
  '',
  '═══════════════════════════════════════════════════════════════',
  '🎯 FINAL AUDIO IMPLEMENTATION CHECKLIST',
  '═══════════════════════════════════════════════════════════════',
  '',
  '✅ Audio Context: lynx.getAudioContext() initialized',
  '✅ Canvas Setup: lynx.createCanvas() with matching name',
  '✅ Analyser Config: createAnalyser() + fftSize = 512',
  '✅ Node Management: connect() to destination + analyser',
  '✅ Lifecycle: onViewDisappeared/onViewAppeared handlers',
  '✅ Animation Loop: lynx.requestAnimationFrame() setup',
  '✅ Visualization: getByteTimeDomainData + getByteFrequencyData',
  '✅ Interaction: Canvas touch events with coordinate conversion',
  '✅ Memory Management: disconnect() and stop() cleanup',
  '✅ Error Handling: Try-catch for audio operations',
  '',
  '',
].join('\n');
